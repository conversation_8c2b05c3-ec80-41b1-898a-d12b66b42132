# 西安地铁线路图

一个现代化的、交互式的西安地铁线路图应用程序，采用模块化架构设计，易于维护和扩展。

## 功能特性

- 🗺️ **交互式地铁线路图**: 基于SVG的高质量矢量图形渲染
- 🔍 **智能搜索**: 快速搜索地铁站点，支持模糊匹配
- 🎯 **站点详情**: 点击站点查看详细信息，包括换乘信息
- 🎨 **线路筛选**: 可以显示/隐藏特定地铁线路
- 📱 **响应式设计**: 适配桌面、平板和移动设备
- ⚡ **高性能**: 优化的渲染引擎，流畅的交互体验
- 🎭 **主题支持**: 支持多种颜色主题和样式配置

## 项目结构

```
metro_ai/
├── src/                          # 源代码目录
│   ├── components/               # 组件目录
│   │   ├── MetroRenderer.js      # 地铁线路图渲染引擎
│   │   └── MetroController.js    # 交互控制器
│   ├── data/                     # 数据目录
│   │   └── metro-lines.js        # 地铁线路数据
│   ├── config/                   # 配置目录
│   │   └── metro-config.js       # 应用配置
│   ├── utils/                    # 工具函数目录
│   │   ├── geometry.js           # 几何计算工具
│   │   ├── color.js              # 颜色处理工具
│   │   └── validation.js         # 数据验证工具
│   ├── types/                    # 类型定义目录
│   │   └── metro.js              # 数据类型定义
│   ├── styles/                   # 样式目录
│   │   ├── metro.css             # 主要样式
│   │   └── ui.css                # UI组件样式
│   └── app.js                    # 应用程序入口
├── docs/                         # 文档目录
│   └── subwaydata.js            # 原始数据文件
├── index.html                    # 主页面
├── package.json                  # 项目配置
└── README.md                     # 项目说明
```

## 技术架构

### 核心组件

1. **MetroRenderer**: 负责SVG渲染和图形绘制
2. **MetroController**: 处理用户交互和状态管理
3. **数据层**: 标准化的地铁线路和站点数据
4. **工具库**: 几何计算、颜色处理、数据验证等工具函数

### 数据结构

#### 站点数据结构
```javascript
{
  id: '0106',                    // 站点唯一标识
  name: '咸阳西站',              // 站点名称
  position: { x: 20, y: 250 },  // 站点坐标
  textPosition: { x: 30, y: 250 }, // 文本位置
  colors: {                      // 颜色配置
    primary: '#00A3E9',
    secondary: '#CBE1F2',
    hidden: '#CBE1F2',
    active: '#00A3E9'
  },
  isTransfer: false,             // 是否为换乘站
  isTerminal: true,              // 是否为终点站
  isCorner: true,                // 是否为转角站点
  transferLines: ['0106'],       // 换乘线路列表
  disabled: false,               // 是否禁用
  metadata: {}                   // 额外元数据
}
```

#### 线路数据结构
```javascript
{
  id: '01',                      // 线路唯一标识
  name: '1号线',                 // 线路名称
  colors: {                      // 线路颜色配置
    primary: '#00A3E9',
    secondary: '#CBE1F2',
    hidden: '#CBE1F2',
    active: '#00A3E9'
  },
  startLabel: { x: 1, y: 205 },  // 起点标签位置
  endLabel: { x: 0, y: 405 },    // 终点标签位置
  stations: [...],               // 站点列表
  disabled: false,               // 是否禁用
  metadata: {}                   // 额外元数据
}
```

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd metro_ai
```

### 2. 启动开发服务器
由于使用了ES6模块，需要通过HTTP服务器访问：

```bash
# 使用Python (推荐)
python -m http.server 8000

# 或使用Node.js
npx http-server

# 或使用PHP
php -S localhost:8000
```

### 3. 访问应用
打开浏览器访问 `http://localhost:8000`

## 使用说明

### 基本操作

1. **查看线路图**: 页面加载后自动显示完整的地铁线路图
2. **搜索站点**: 在左侧搜索框输入站点名称进行搜索
3. **选择站点**: 点击地图上的站点查看详细信息
4. **筛选线路**: 使用左侧的线路开关控制线路显示/隐藏
5. **缩放控制**: 使用右上角的缩放按钮调整视图

### 高级功能

- **换乘站识别**: 换乘站用加粗边框显示
- **线路高亮**: 选择站点时自动高亮相关线路
- **响应式布局**: 自动适配不同屏幕尺寸
- **全屏模式**: 点击全屏按钮获得更好的查看体验

## 自定义配置

### 修改主题颜色
编辑 `src/config/metro-config.js` 中的 `LINE_COLOR_THEMES`:

```javascript
export const LINE_COLOR_THEMES = {
  default: {
    '01': { primary: '#00A3E9', secondary: '#CBE1F2' },
    // 添加或修改线路颜色
  }
};
```

### 调整样式配置
修改 `src/config/metro-config.js` 中的 `DEFAULT_METRO_CONFIG`:

```javascript
export const DEFAULT_METRO_CONFIG = {
  station: {
    radius: 6,        // 站点圆半径
    strokeWidth: 2,   // 边框宽度
  },
  line: {
    strokeWidth: 4,   // 线路宽度
  },
  // 其他配置...
};
```

## 扩展开发

### 添加新线路

1. 在 `src/data/metro-lines.js` 中添加线路数据
2. 在 `src/config/metro-config.js` 中添加线路颜色配置
3. 重新加载页面查看效果

### 添加新功能

1. 在相应的组件中添加功能代码
2. 更新事件监听器和状态管理
3. 添加相应的UI控件和样式

### 数据验证

使用内置的验证工具确保数据完整性：

```javascript
import { validateMetroSystem } from './src/utils/validation.js';

const validation = validateMetroSystem(metroData);
if (!validation.isValid) {
  console.error('Data validation failed:', validation.errors);
}
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 性能优化

- SVG矢量图形，支持无损缩放
- 事件委托减少内存占用
- 响应式图片和样式
- 模块化加载，按需引入

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 基础地铁线路图渲染功能
- 站点搜索和筛选功能
- 响应式设计支持
- 交互式用户界面
