<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>西安地铁线路图</title>
    <link rel="stylesheet" href="src/styles/metro.css">
    <link rel="stylesheet" href="src/styles/ui.css">
</head>
<body>
    <div class="metro-app">
        <!-- 头部导航 -->
        <header class="metro-header">
            <div class="header-content">
                <h1 class="app-title">西安地铁线路图</h1>
                <div class="header-controls">
                    <button class="btn btn-primary" id="resetBtn">重置</button>
                    <button class="btn btn-secondary" id="fullscreenBtn">全屏</button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="metro-main">
            <!-- 侧边栏 -->
            <aside class="metro-sidebar">
                <!-- 搜索区域 -->
                <div class="search-section">
                    <h3>站点搜索</h3>
                    <div class="search-box">
                        <input type="text" id="stationSearch" placeholder="输入站点名称..." class="search-input">
                        <button class="search-btn" id="searchBtn">搜索</button>
                    </div>
                    <div class="search-results" id="searchResults"></div>
                </div>

                <!-- 线路过滤 -->
                <div class="filter-section">
                    <h3>线路筛选</h3>
                    <div class="line-filters" id="lineFilters">
                        <!-- 线路开关将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 图例 -->
                <div class="legend-section">
                    <h3>图例</h3>
                    <div class="legend-items">
                        <div class="legend-item">
                            <div class="legend-symbol station-normal"></div>
                            <span>普通站点</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-symbol station-transfer"></div>
                            <span>换乘站点</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-symbol station-terminal"></div>
                            <span>终点站</span>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 地铁线路图容器 -->
            <div class="metro-container">
                <div class="metro-canvas" id="metroCanvas">
                    <!-- SVG地铁线路图将在这里渲染 -->
                </div>
                
                <!-- 缩放控制 -->
                <div class="zoom-controls">
                    <button class="zoom-btn" id="zoomInBtn">+</button>
                    <button class="zoom-btn" id="zoomOutBtn">-</button>
                    <button class="zoom-btn" id="fitBtn">适应</button>
                </div>
            </div>

            <!-- 信息面板 -->
            <div class="info-panel" id="infoPanel">
                <div class="info-header">
                    <h3>站点信息</h3>
                    <button class="close-btn" id="closePanelBtn">&times;</button>
                </div>
                <div class="info-content" id="infoContent">
                    <p class="info-placeholder">点击站点查看详细信息</p>
                </div>
            </div>
        </main>

        <!-- 提示框 -->
        <div class="tooltip" id="tooltip">
            <div class="tooltip-content" id="tooltipContent"></div>
        </div>

        <!-- 加载指示器 -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <p>正在加载地铁线路图...</p>
        </div>
    </div>

    <!-- JavaScript模块 -->
    <script type="module" src="src/app.js"></script>
</body>
</html>
