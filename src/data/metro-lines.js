/**
 * 西安地铁线路数据
 * @fileoverview 重构后的地铁线路数据，采用标准化的数据结构
 */

import { LINE_COLOR_THEMES } from '../config/metro-config.js';

/**
 * 创建站点对象
 * @param {Object} rawStation - 原始站点数据
 * @param {string} lineId - 线路ID
 * @returns {Object} 标准化的站点对象
 */
function createStation(rawStation, lineId) {
  const colors = LINE_COLOR_THEMES.default[lineId];

  return {
    id: rawStation.stationId,
    name: rawStation.name || '',
    position: {
      x: rawStation.px,
      y: rawStation.py
    },
    textPosition: {
      x: rawStation.textpx,
      y: rawStation.textpy
    },
    colors: {
      primary: rawStation.showColor || colors.primary,
      secondary: rawStation.hideColor || colors.secondary,
      hidden: rawStation.hideColor || colors.secondary,
      active: rawStation.showColor || colors.primary
    },
    isTransfer: rawStation.transfer || false,
    isTerminal: rawStation.firstStation || false,
    isCorner: rawStation.corner || false,
    transferLines: Array.isArray(rawStation.stationId) ? rawStation.stationId : [rawStation.stationId],
    disabled: rawStation.disabled || false,
    showNameFlag: rawStation.showNameFlag || false,
    metadata: {
      startName: rawStation.startName,
      endName: rawStation.endName
    }
  };
}

/**
 * 创建线路对象
 * @param {string} lineId - 线路ID
 * @param {Object} rawLine - 原始线路数据
 * @returns {Object} 标准化的线路对象
 */
function createMetroLine(lineId, rawLine) {
  const colors = LINE_COLOR_THEMES.default[lineId];

  return {
    id: lineId,
    name: rawLine.name,
    colors: {
      primary: rawLine.color || colors.primary,
      secondary: rawLine.hideColor || colors.secondary,
      hidden: rawLine.hideColor || colors.secondary,
      active: rawLine.showColor || rawLine.color || colors.primary
    },
    startLabel: {
      x: rawLine.px,
      y: rawLine.py
    },
    endLabel: {
      x: rawLine.ex,
      y: rawLine.ey
    },
    stations: rawLine.data.map(station => createStation(station, lineId)),
    disabled: false,
    metadata: {
      originalId: lineId
    }
  };
}

// 原始线路数据（从原文件转换而来）
const rawLineData = {
  '01': {
    name: '1号线',
    px: 1,
    py: 205,
    ex: 0,
    ey: 405,
    color: '#00A3E9',
    hideColor: '#CBE1F2',
    showColor: '#00A3E9',
    data: [
      {
        px: 20, py: 250, name: '咸阳西站', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: -15, textpy: 250, stationId: '0106', corner: true, firstStation: true
      },
      {
        px: 20, py: 290, name: '宝泉路', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: -15, textpy: 290, stationId: '0107'
      },
      {
        px: 20, py: 330, name: '中华西路', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: -15, textpy: 330, stationId: '0108', corner: true, firstStation: true
      },
      {
        px: 20, py: 370, name: '', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 330, textpy: 386, stationId: '0108G0109'
      },
      {
        px: 40, py: 370, name: '安谷', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: -15, textpy: 385, stationId: '0109', corner: true
      },
      {
        px: 80, py: 370, name: '陈杨寨', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 65, textpy: 385, stationId: '0110'
      },
      {
        px: 120, py: 370, name: '白马河', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 105, textpy: 385, stationId: '0111'
      },
      {
        px: 150, py: 370, name: '陕西中医药大学', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 120, textpy: 355, stationId: '0112'
      },
      {
        px: 190, py: 370, name: '沣河森林公园', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 160, textpy: 385, stationId: '0113'
      },
      {
        px: 220, py: 370, name: '北槐', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 210, textpy: 355, stationId: '0115'
      },
      {
        px: 250, py: 370, name: '上林路', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 255, textpy: 390, stationId: ['1633', '0117'], transfer: true
      },
      {
        px: 280, py: 370, name: '沣东自贸园', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 260, textpy: 355, stationId: '0119'
      },
      {
        px: 310, py: 370, name: '后卫寨', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 320, textpy: 370, stationId: '0121', corner: true, firstStation: true
      },
      {
        px: 330, py: 447, name: '三桥', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 335, textpy: 435, stationId: '0123', corner: true
      },
      {
        px: 360, py: 447, name: '皂河', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 335, textpy: 465, stationId: '0125'
      },
      {
        px: 390, py: 447, name: '枣园', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 365, textpy: 435, stationId: '0127'
      },
      {
        px: 410, py: 447, name: '汉城路', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 395, textpy: 465, stationId: '0129'
      },
      {
        px: 440, py: 447, name: '开远门', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 397, textpy: 430, stationId: '0131'
      },
      {
        px: 500, py: 447, name: '劳动路', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 485, textpy: 465, stationId: '0133'
      },
      {
        px: 530, py: 447, name: '玉祥门', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 510, textpy: 435, stationId: '0135'
      },
      {
        px: 560, py: 447, name: '洒金桥', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 548, textpy: 434, stationId: '0137'
      },
      {
        px: 600, py: 446.5, name: '北大街', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 605, textpy: 435, stationId: ['0139', '0239'], transfer: true
      },
      {
        px: 689, py: 446.5, name: '五路口', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 645, textpy: 435, stationId: ['0449', '0141'], transfer: true
      },
      {
        px: 717, py: 447, name: '朝阳门', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 700, textpy: 435, stationId: '0143'
      },
      {
        px: 779, py: 447, name: '康复路', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 759, textpy: 435, stationId: '0145'
      },
      {
        px: 814, py: 446.5, name: '通化门', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 810, textpy: 435, stationId: ['0347', '0147'], transfer: true, showNameFlag: true
      },
      {
        px: 875, py: 447, name: '万寿路', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 835, textpy: 460, stationId: '0149'
      },
      {
        px: 905, py: 447, name: '长乐坡', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 890, textpy: 435, stationId: '0151'
      },
      {
        px: 925, py: 447, name: '浐河', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 912, textpy: 465, stationId: '0153'
      },
      {
        px: 950, py: 447, name: '半坡', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 940, textpy: 465, stationId: '0155'
      },
      {
        px: 980, py: 447, name: '纺织城', hideColor: '#CBE1F2', showColor: '#00A3E9',
        textpx: 960, textpy: 434, stationId: ['0921', '0683', '0157'], transfer: true
      }
    ]
  }
};

/**
 * 西安地铁线路数据
 */
export const XIAN_METRO_LINES = {};

// 转换所有线路数据
Object.keys(rawLineData).forEach(lineId => {
  XIAN_METRO_LINES[lineId] = createMetroLine(lineId, rawLineData[lineId]);
});

/**
 * 获取所有线路
 * @returns {Object} 所有线路数据
 */
export function getAllLines() {
  return XIAN_METRO_LINES;
}

/**
 * 根据ID获取线路
 * @param {string} lineId - 线路ID
 * @returns {Object|null} 线路数据
 */
export function getLineById(lineId) {
  return XIAN_METRO_LINES[lineId] || null;
}

/**
 * 获取所有换乘站
 * @returns {Array} 换乘站列表
 */
export function getTransferStations() {
  const transferStations = [];

  Object.values(XIAN_METRO_LINES).forEach(line => {
    line.stations.forEach(station => {
      if (station.isTransfer) {
        transferStations.push({
          ...station,
          lineId: line.id,
          lineName: line.name
        });
      }
    });
  });

  return transferStations;
}

/**
 * 根据站点ID查找站点
 * @param {string} stationId - 站点ID
 * @returns {Array} 包含该站点的所有线路信息
 */
export function findStationById(stationId) {
  const results = [];

  Object.values(XIAN_METRO_LINES).forEach(line => {
    line.stations.forEach(station => {
      if (station.transferLines.includes(stationId)) {
        results.push({
          station,
          line: {
            id: line.id,
            name: line.name,
            colors: line.colors
          }
        });
      }
    });
  });

  return results;
}
