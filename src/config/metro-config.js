/**
 * 地铁线路图配置文件
 * @fileoverview 管理地铁线路图的全局配置项
 */

/**
 * 默认地铁系统配置
 */
export const DEFAULT_METRO_CONFIG = {
  // 画布配置
  canvas: {
    width: 1200,
    height: 900,
    backgroundColor: '#ffffff',
    padding: 20
  },

  // 站点样式配置
  station: {
    radius: 6,
    strokeWidth: 2,
    hoverRadius: 8,
    selectedRadius: 10,
    textOffset: {
      x: 10,
      y: 5
    }
  },

  // 线路样式配置
  line: {
    strokeWidth: 4,
    hoverStrokeWidth: 6,
    selectedStrokeWidth: 8,
    cornerRadius: 5
  },

  // 文本样式配置
  text: {
    fontFamily: 'Arial, sans-serif',
    fontSize: 12,
    fontWeight: 'normal',
    color: '#333333',
    hoverColor: '#000000',
    selectedColor: '#ffffff'
  },

  // 动画配置
  animation: {
    duration: 300,
    easing: 'ease-in-out',
    enabled: true
  },

  // 交互配置
  interaction: {
    enableHover: true,
    enableClick: true,
    enableZoom: true,
    enablePan: true,
    zoomRange: {
      min: 0.5,
      max: 3.0
    }
  },

  // 主题配置
  theme: {
    name: 'default',
    colors: {
      background: '#ffffff',
      text: '#333333',
      border: '#cccccc',
      hover: '#f0f0f0',
      selected: '#007bff'
    }
  }
};

/**
 * 线路颜色主题
 */
export const LINE_COLOR_THEMES = {
  default: {
    '01': { primary: '#00A3E9', secondary: '#CBE1F2' }, // 1号线
    '02': { primary: '#E62128', secondary: '#E3CACA' }, // 2号线
    '03': { primary: '#BC80A8', secondary: '#DFD9E9' }, // 3号线
    '04': { primary: '#02AF8E', secondary: '#CEE4EA' }, // 4号线
    '05': { primary: '#C4D70D', secondary: '#ddded0' }, // 5号线
    '06': { primary: '#3C4396', secondary: '#d1d4f0' }, // 6号线
    '08': { primary: '#dfab1a', secondary: '#f5e6a3' }, // 8号线
    '09': { primary: '#8B4513', secondary: '#D2B48C' }, // 9号线
    '14': { primary: '#FF6B35', secondary: '#FFD1C1' }, // 14号线
    '16': { primary: '#9B59B6', secondary: '#E8D5E8' }  // 16号线
  }
};

/**
 * 响应式断点配置
 */
export const RESPONSIVE_BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1200
};

/**
 * 获取响应式配置
 * @param {number} screenWidth - 屏幕宽度
 * @returns {Object} 响应式配置
 */
export function getResponsiveConfig(screenWidth) {
  const baseConfig = { ...DEFAULT_METRO_CONFIG };
  
  if (screenWidth <= RESPONSIVE_BREAKPOINTS.mobile) {
    // 移动端配置
    baseConfig.canvas.width = screenWidth - 40;
    baseConfig.canvas.height = 600;
    baseConfig.station.radius = 4;
    baseConfig.text.fontSize = 10;
    baseConfig.line.strokeWidth = 3;
  } else if (screenWidth <= RESPONSIVE_BREAKPOINTS.tablet) {
    // 平板端配置
    baseConfig.canvas.width = screenWidth - 60;
    baseConfig.canvas.height = 700;
    baseConfig.station.radius = 5;
    baseConfig.text.fontSize = 11;
    baseConfig.line.strokeWidth = 3.5;
  }
  
  return baseConfig;
}

/**
 * 合并配置
 * @param {Object} userConfig - 用户自定义配置
 * @returns {Object} 合并后的配置
 */
export function mergeConfig(userConfig = {}) {
  return {
    ...DEFAULT_METRO_CONFIG,
    ...userConfig,
    canvas: { ...DEFAULT_METRO_CONFIG.canvas, ...userConfig.canvas },
    station: { ...DEFAULT_METRO_CONFIG.station, ...userConfig.station },
    line: { ...DEFAULT_METRO_CONFIG.line, ...userConfig.line },
    text: { ...DEFAULT_METRO_CONFIG.text, ...userConfig.text },
    animation: { ...DEFAULT_METRO_CONFIG.animation, ...userConfig.animation },
    interaction: { ...DEFAULT_METRO_CONFIG.interaction, ...userConfig.interaction },
    theme: { ...DEFAULT_METRO_CONFIG.theme, ...userConfig.theme }
  };
}
