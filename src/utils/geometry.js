/**
 * 几何计算工具函数
 * @fileoverview 提供坐标转换、距离计算等几何相关的工具函数
 */

/**
 * 计算两点之间的距离
 * @param {Object} point1 - 第一个点 {x, y}
 * @param {Object} point2 - 第二个点 {x, y}
 * @returns {number} 距离
 */
export function calculateDistance(point1, point2) {
  const dx = point2.x - point1.x;
  const dy = point2.y - point1.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * 计算点到线段的最短距离
 * @param {Object} point - 点坐标 {x, y}
 * @param {Object} lineStart - 线段起点 {x, y}
 * @param {Object} lineEnd - 线段终点 {x, y}
 * @returns {number} 最短距离
 */
export function pointToLineDistance(point, lineStart, lineEnd) {
  const A = point.x - lineStart.x;
  const B = point.y - lineStart.y;
  const C = lineEnd.x - lineStart.x;
  const D = lineEnd.y - lineStart.y;

  const dot = A * C + B * D;
  const lenSq = C * C + D * D;
  
  if (lenSq === 0) {
    return calculateDistance(point, lineStart);
  }
  
  let param = dot / lenSq;
  
  let xx, yy;
  
  if (param < 0) {
    xx = lineStart.x;
    yy = lineStart.y;
  } else if (param > 1) {
    xx = lineEnd.x;
    yy = lineEnd.y;
  } else {
    xx = lineStart.x + param * C;
    yy = lineStart.y + param * D;
  }
  
  return calculateDistance(point, { x: xx, y: yy });
}

/**
 * 检查点是否在矩形内
 * @param {Object} point - 点坐标 {x, y}
 * @param {Object} rect - 矩形 {x, y, width, height}
 * @returns {boolean} 是否在矩形内
 */
export function isPointInRect(point, rect) {
  return point.x >= rect.x && 
         point.x <= rect.x + rect.width &&
         point.y >= rect.y && 
         point.y <= rect.y + rect.height;
}

/**
 * 检查点是否在圆内
 * @param {Object} point - 点坐标 {x, y}
 * @param {Object} circle - 圆 {x, y, radius}
 * @returns {boolean} 是否在圆内
 */
export function isPointInCircle(point, circle) {
  const distance = calculateDistance(point, { x: circle.x, y: circle.y });
  return distance <= circle.radius;
}

/**
 * 计算两个矩形的交集
 * @param {Object} rect1 - 第一个矩形 {x, y, width, height}
 * @param {Object} rect2 - 第二个矩形 {x, y, width, height}
 * @returns {Object|null} 交集矩形或null
 */
export function getRectIntersection(rect1, rect2) {
  const left = Math.max(rect1.x, rect2.x);
  const right = Math.min(rect1.x + rect1.width, rect2.x + rect2.width);
  const top = Math.max(rect1.y, rect2.y);
  const bottom = Math.min(rect1.y + rect1.height, rect2.y + rect2.height);
  
  if (left < right && top < bottom) {
    return {
      x: left,
      y: top,
      width: right - left,
      height: bottom - top
    };
  }
  
  return null;
}

/**
 * 计算角度（弧度）
 * @param {Object} center - 中心点 {x, y}
 * @param {Object} point - 目标点 {x, y}
 * @returns {number} 角度（弧度）
 */
export function calculateAngle(center, point) {
  return Math.atan2(point.y - center.y, point.x - center.x);
}

/**
 * 弧度转角度
 * @param {number} radians - 弧度
 * @returns {number} 角度
 */
export function radiansToDegrees(radians) {
  return radians * (180 / Math.PI);
}

/**
 * 角度转弧度
 * @param {number} degrees - 角度
 * @returns {number} 弧度
 */
export function degreesToRadians(degrees) {
  return degrees * (Math.PI / 180);
}

/**
 * 计算贝塞尔曲线上的点
 * @param {number} t - 参数 (0-1)
 * @param {Object} p0 - 起点 {x, y}
 * @param {Object} p1 - 控制点1 {x, y}
 * @param {Object} p2 - 控制点2 {x, y}
 * @param {Object} p3 - 终点 {x, y}
 * @returns {Object} 曲线上的点 {x, y}
 */
export function getBezierPoint(t, p0, p1, p2, p3) {
  const u = 1 - t;
  const tt = t * t;
  const uu = u * u;
  const uuu = uu * u;
  const ttt = tt * t;
  
  return {
    x: uuu * p0.x + 3 * uu * t * p1.x + 3 * u * tt * p2.x + ttt * p3.x,
    y: uuu * p0.y + 3 * uu * t * p1.y + 3 * u * tt * p2.y + ttt * p3.y
  };
}

/**
 * 限制数值在指定范围内
 * @param {number} value - 数值
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 限制后的数值
 */
export function clamp(value, min, max) {
  return Math.min(Math.max(value, min), max);
}

/**
 * 线性插值
 * @param {number} start - 起始值
 * @param {number} end - 结束值
 * @param {number} t - 插值参数 (0-1)
 * @returns {number} 插值结果
 */
export function lerp(start, end, t) {
  return start + (end - start) * t;
}

/**
 * 计算边界框
 * @param {Array} points - 点数组 [{x, y}, ...]
 * @returns {Object} 边界框 {x, y, width, height}
 */
export function getBoundingBox(points) {
  if (points.length === 0) {
    return { x: 0, y: 0, width: 0, height: 0 };
  }
  
  let minX = points[0].x;
  let maxX = points[0].x;
  let minY = points[0].y;
  let maxY = points[0].y;
  
  for (let i = 1; i < points.length; i++) {
    minX = Math.min(minX, points[i].x);
    maxX = Math.max(maxX, points[i].x);
    minY = Math.min(minY, points[i].y);
    maxY = Math.max(maxY, points[i].y);
  }
  
  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY
  };
}
