/**
 * 数据验证工具函数
 * @fileoverview 提供地铁数据的验证功能
 */

/**
 * 验证位置坐标
 * @param {Object} position - 位置对象
 * @returns {boolean} 是否有效
 */
export function validatePosition(position) {
  return position && 
         typeof position === 'object' &&
         typeof position.x === 'number' &&
         typeof position.y === 'number' &&
         !isNaN(position.x) &&
         !isNaN(position.y);
}

/**
 * 验证颜色配置
 * @param {Object} colors - 颜色配置对象
 * @returns {boolean} 是否有效
 */
export function validateColorConfig(colors) {
  if (!colors || typeof colors !== 'object') {
    return false;
  }
  
  const requiredColors = ['primary', 'secondary', 'hidden', 'active'];
  return requiredColors.every(color => 
    typeof colors[color] === 'string' && colors[color].length > 0
  );
}

/**
 * 验证站点数据
 * @param {Object} station - 站点对象
 * @returns {Object} 验证结果 {isValid, errors}
 */
export function validateStation(station) {
  const errors = [];
  
  if (!station || typeof station !== 'object') {
    return { isValid: false, errors: ['Station must be an object'] };
  }
  
  // 验证必需字段
  if (!station.id || typeof station.id !== 'string') {
    errors.push('Station id is required and must be a string');
  }
  
  if (typeof station.name !== 'string') {
    errors.push('Station name must be a string');
  }
  
  // 验证位置
  if (!validatePosition(station.position)) {
    errors.push('Station position is invalid');
  }
  
  if (!validatePosition(station.textPosition)) {
    errors.push('Station textPosition is invalid');
  }
  
  // 验证颜色配置
  if (!validateColorConfig(station.colors)) {
    errors.push('Station colors configuration is invalid');
  }
  
  // 验证布尔值
  const booleanFields = ['isTransfer', 'isTerminal', 'isCorner', 'disabled'];
  booleanFields.forEach(field => {
    if (station[field] !== undefined && typeof station[field] !== 'boolean') {
      errors.push(`Station ${field} must be a boolean`);
    }
  });
  
  // 验证换乘线路
  if (station.transferLines && !Array.isArray(station.transferLines)) {
    errors.push('Station transferLines must be an array');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证线路数据
 * @param {Object} line - 线路对象
 * @returns {Object} 验证结果 {isValid, errors}
 */
export function validateMetroLine(line) {
  const errors = [];
  
  if (!line || typeof line !== 'object') {
    return { isValid: false, errors: ['Line must be an object'] };
  }
  
  // 验证必需字段
  if (!line.id || typeof line.id !== 'string') {
    errors.push('Line id is required and must be a string');
  }
  
  if (!line.name || typeof line.name !== 'string') {
    errors.push('Line name is required and must be a string');
  }
  
  // 验证颜色配置
  if (!validateColorConfig(line.colors)) {
    errors.push('Line colors configuration is invalid');
  }
  
  // 验证标签位置
  if (!validatePosition(line.startLabel)) {
    errors.push('Line startLabel position is invalid');
  }
  
  if (!validatePosition(line.endLabel)) {
    errors.push('Line endLabel position is invalid');
  }
  
  // 验证站点数组
  if (!Array.isArray(line.stations)) {
    errors.push('Line stations must be an array');
  } else {
    line.stations.forEach((station, index) => {
      const stationValidation = validateStation(station);
      if (!stationValidation.isValid) {
        errors.push(`Station ${index}: ${stationValidation.errors.join(', ')}`);
      }
    });
  }
  
  // 验证布尔值
  if (line.disabled !== undefined && typeof line.disabled !== 'boolean') {
    errors.push('Line disabled must be a boolean');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证地铁系统配置
 * @param {Object} config - 配置对象
 * @returns {Object} 验证结果 {isValid, errors}
 */
export function validateMetroConfig(config) {
  const errors = [];
  
  if (!config || typeof config !== 'object') {
    return { isValid: false, errors: ['Config must be an object'] };
  }
  
  // 验证画布配置
  if (config.canvas) {
    if (typeof config.canvas.width !== 'number' || config.canvas.width <= 0) {
      errors.push('Canvas width must be a positive number');
    }
    if (typeof config.canvas.height !== 'number' || config.canvas.height <= 0) {
      errors.push('Canvas height must be a positive number');
    }
  }
  
  // 验证站点配置
  if (config.station) {
    if (typeof config.station.radius !== 'number' || config.station.radius <= 0) {
      errors.push('Station radius must be a positive number');
    }
    if (typeof config.station.strokeWidth !== 'number' || config.station.strokeWidth < 0) {
      errors.push('Station strokeWidth must be a non-negative number');
    }
  }
  
  // 验证线路配置
  if (config.line) {
    if (typeof config.line.strokeWidth !== 'number' || config.line.strokeWidth <= 0) {
      errors.push('Line strokeWidth must be a positive number');
    }
  }
  
  // 验证文本配置
  if (config.text) {
    if (config.text.fontSize && (typeof config.text.fontSize !== 'number' || config.text.fontSize <= 0)) {
      errors.push('Text fontSize must be a positive number');
    }
  }
  
  // 验证动画配置
  if (config.animation) {
    if (config.animation.duration && (typeof config.animation.duration !== 'number' || config.animation.duration < 0)) {
      errors.push('Animation duration must be a non-negative number');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证地铁系统数据完整性
 * @param {Object} metroData - 地铁系统数据
 * @returns {Object} 验证结果 {isValid, errors, warnings}
 */
export function validateMetroSystem(metroData) {
  const errors = [];
  const warnings = [];
  
  if (!metroData || typeof metroData !== 'object') {
    return { isValid: false, errors: ['Metro data must be an object'], warnings: [] };
  }
  
  // 验证线路数据
  const lines = Object.values(metroData);
  if (lines.length === 0) {
    warnings.push('No metro lines found');
  }
  
  const stationIds = new Set();
  const transferStations = new Map();
  
  lines.forEach((line, lineIndex) => {
    const lineValidation = validateMetroLine(line);
    if (!lineValidation.isValid) {
      errors.push(`Line ${lineIndex} (${line.id || 'unknown'}): ${lineValidation.errors.join(', ')}`);
    }
    
    // 检查站点ID重复
    if (line.stations) {
      line.stations.forEach(station => {
        if (station.id) {
          if (stationIds.has(station.id)) {
            warnings.push(`Duplicate station ID: ${station.id}`);
          } else {
            stationIds.add(station.id);
          }
          
          // 收集换乘站信息
          if (station.isTransfer) {
            if (!transferStations.has(station.id)) {
              transferStations.set(station.id, []);
            }
            transferStations.get(station.id).push(line.id);
          }
        }
      });
    }
  });
  
  // 验证换乘站的一致性
  transferStations.forEach((lineIds, stationId) => {
    if (lineIds.length < 2) {
      warnings.push(`Transfer station ${stationId} is only on one line: ${lineIds[0]}`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 清理和标准化站点数据
 * @param {Object} station - 原始站点数据
 * @returns {Object} 清理后的站点数据
 */
export function sanitizeStation(station) {
  if (!station || typeof station !== 'object') {
    return null;
  }
  
  return {
    id: String(station.id || ''),
    name: String(station.name || ''),
    position: {
      x: Number(station.position?.x) || 0,
      y: Number(station.position?.y) || 0
    },
    textPosition: {
      x: Number(station.textPosition?.x) || 0,
      y: Number(station.textPosition?.y) || 0
    },
    colors: {
      primary: String(station.colors?.primary || '#000000'),
      secondary: String(station.colors?.secondary || '#cccccc'),
      hidden: String(station.colors?.hidden || '#cccccc'),
      active: String(station.colors?.active || '#000000')
    },
    isTransfer: Boolean(station.isTransfer),
    isTerminal: Boolean(station.isTerminal),
    isCorner: Boolean(station.isCorner),
    transferLines: Array.isArray(station.transferLines) ? station.transferLines : [station.id],
    disabled: Boolean(station.disabled),
    showNameFlag: Boolean(station.showNameFlag),
    metadata: station.metadata || {}
  };
}
