/**
 * 地铁线路图主要样式
 */

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

/* 应用主容器 */
.metro-app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

/* 头部样式 */
.metro-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.app-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.header-controls {
    display: flex;
    gap: 0.5rem;
}

/* 主要内容区域 */
.metro-main {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 侧边栏样式 */
.metro-sidebar {
    width: 300px;
    background: white;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    padding: 1rem;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
}

.metro-sidebar h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #555;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.25rem;
}

/* 搜索区域 */
.search-section {
    margin-bottom: 2rem;
}

.search-box {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.search-input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.search-btn {
    padding: 0.5rem 1rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.search-btn:hover {
    background: #5a67d8;
}

.search-results {
    max-height: 200px;
    overflow-y: auto;
}

.search-result-item {
    padding: 0.5rem;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-result-item:hover {
    background: #f8f9ff;
}

.search-result-station {
    font-weight: 600;
    color: #333;
}

.search-result-line {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

/* 过滤区域 */
.filter-section {
    margin-bottom: 2rem;
}

.line-filters {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.line-filter-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.line-filter-item:hover {
    background: #f8f9ff;
}

.line-filter-checkbox {
    margin: 0;
}

.line-filter-color {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    border: 1px solid #ddd;
}

.line-filter-name {
    flex: 1;
    font-size: 0.9rem;
}

/* 图例区域 */
.legend-section {
    margin-bottom: 2rem;
}

.legend-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.legend-symbol {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #333;
}

.station-normal {
    background: white;
}

.station-transfer {
    background: white;
    border-width: 3px;
}

.station-terminal {
    background: #333;
}

/* 地铁线路图容器 */
.metro-container {
    flex: 1;
    position: relative;
    background: white;
    overflow: hidden;
}

.metro-canvas {
    width: 100%;
    height: 100%;
    position: relative;
    cursor: grab;
}

.metro-canvas:active {
    cursor: grabbing;
}

.metro-canvas svg {
    width: 100%;
    height: 100%;
    display: block;
}

/* 缩放控制 */
.zoom-controls {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    z-index: 100;
}

.zoom-btn {
    width: 40px;
    height: 40px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
}

.zoom-btn:hover {
    background: #f8f9ff;
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 信息面板 */
.info-panel {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 300px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 200;
    transform: translateX(-320px);
    transition: transform 0.3s ease;
}

.info-panel.show {
    transform: translateX(0);
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
    background: #f8f9ff;
    border-radius: 8px 8px 0 0;
}

.info-header h3 {
    margin: 0;
    font-size: 1rem;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.close-btn:hover {
    background: #eee;
    color: #333;
}

.info-content {
    padding: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.info-placeholder {
    color: #999;
    font-style: italic;
    text-align: center;
    margin: 2rem 0;
}

/* 提示框 */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.2s;
}

.tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.3s;
}

.loading-overlay.hide {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .metro-sidebar {
        width: 250px;
    }
    
    .info-panel {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .metro-main {
        flex-direction: column;
    }
    
    .metro-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
    }
    
    .metro-container {
        flex: 1;
    }
    
    .info-panel {
        position: fixed;
        top: auto;
        bottom: 0;
        left: 0;
        right: 0;
        width: auto;
        border-radius: 8px 8px 0 0;
        transform: translateY(100%);
    }
    
    .info-panel.show {
        transform: translateY(0);
    }
    
    .header-content {
        padding: 0 1rem;
    }
    
    .app-title {
        font-size: 1.2rem;
    }
}
