/**
 * 地铁线路图交互控制器
 * @fileoverview 处理用户交互、状态管理和事件响应
 */

import { MetroRenderer } from './MetroRenderer.js';
import { getAllLines, findStationById, getTransferStations } from '../data/metro-lines.js';
import { validateMetroSystem } from '../utils/validation.js';

/**
 * 地铁线路图控制器
 */
export class MetroController {
  /**
   * 构造函数
   * @param {HTMLElement} container - 容器元素
   * @param {Object} options - 配置选项
   */
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      enableSearch: true,
      enableFilter: true,
      enableInfo: true,
      enableRouting: false,
      ...options
    };
    
    this.renderer = null;
    this.metroData = null;
    this.currentState = {
      selectedStation: null,
      selectedLine: null,
      hoveredStation: null,
      visibleLines: new Set(),
      searchResults: [],
      filterCriteria: {}
    };
    
    this.eventCallbacks = new Map();
    
    this.init();
  }

  /**
   * 初始化控制器
   */
  async init() {
    try {
      // 加载地铁数据
      await this.loadMetroData();
      
      // 创建渲染器
      this.renderer = new MetroRenderer(this.container, this.options.rendererConfig);
      
      // 设置事件监听
      this.setupEventListeners();
      
      // 初始渲染
      this.render();
      
      // 创建UI控件
      if (this.options.enableSearch) {
        this.createSearchUI();
      }
      
      if (this.options.enableFilter) {
        this.createFilterUI();
      }
      
      if (this.options.enableInfo) {
        this.createInfoPanel();
      }
      
      this.emit('initialized');
      
    } catch (error) {
      console.error('Failed to initialize MetroController:', error);
      this.emit('error', { error });
    }
  }

  /**
   * 加载地铁数据
   */
  async loadMetroData() {
    this.metroData = getAllLines();
    
    // 验证数据
    const validation = validateMetroSystem(this.metroData);
    if (!validation.isValid) {
      console.warn('Metro data validation failed:', validation.errors);
    }
    
    if (validation.warnings.length > 0) {
      console.warn('Metro data warnings:', validation.warnings);
    }
    
    // 初始化可见线路
    this.currentState.visibleLines = new Set(Object.keys(this.metroData));
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    if (this.renderer) {
      this.renderer.on('stationSelected', this.handleStationSelected.bind(this));
      this.renderer.on('stationHovered', this.handleStationHovered.bind(this));
      this.renderer.on('lineSelected', this.handleLineSelected.bind(this));
    }
  }

  /**
   * 渲染地铁线路图
   */
  render() {
    if (this.renderer && this.metroData) {
      // 过滤可见线路
      const visibleData = {};
      this.currentState.visibleLines.forEach(lineId => {
        if (this.metroData[lineId]) {
          visibleData[lineId] = this.metroData[lineId];
        }
      });
      
      this.renderer.render(visibleData);
    }
  }

  /**
   * 处理站点选择事件
   * @param {Object} data - 事件数据
   */
  handleStationSelected(data) {
    const { stationId, lineId } = data;
    this.currentState.selectedStation = { stationId, lineId };
    
    // 更新信息面板
    this.updateInfoPanel(stationId);
    
    // 高亮相关线路
    this.highlightTransferLines(stationId);
    
    this.emit('stationSelected', data);
  }

  /**
   * 处理站点悬停事件
   * @param {Object} data - 事件数据
   */
  handleStationHovered(data) {
    const { stationId } = data;
    this.currentState.hoveredStation = stationId;
    
    if (stationId) {
      this.showStationTooltip(stationId);
    } else {
      this.hideStationTooltip();
    }
    
    this.emit('stationHovered', data);
  }

  /**
   * 处理线路选择事件
   * @param {Object} data - 事件数据
   */
  handleLineSelected(data) {
    const { lineId } = data;
    this.currentState.selectedLine = lineId;
    
    // 高亮选中线路的所有站点
    this.highlightLineStations(lineId);
    
    this.emit('lineSelected', data);
  }

  /**
   * 搜索站点
   * @param {string} query - 搜索关键词
   * @returns {Array} 搜索结果
   */
  searchStations(query) {
    if (!query || query.trim() === '') {
      this.currentState.searchResults = [];
      return [];
    }
    
    const results = [];
    const queryLower = query.toLowerCase().trim();
    
    Object.values(this.metroData).forEach(line => {
      line.stations.forEach(station => {
        if (station.name && station.name.toLowerCase().includes(queryLower)) {
          results.push({
            station,
            line: {
              id: line.id,
              name: line.name,
              colors: line.colors
            }
          });
        }
      });
    });
    
    this.currentState.searchResults = results;
    this.emit('searchResults', { query, results });
    
    return results;
  }

  /**
   * 过滤线路
   * @param {Object} criteria - 过滤条件
   */
  filterLines(criteria) {
    this.currentState.filterCriteria = criteria;
    
    const visibleLines = new Set();
    
    Object.entries(this.metroData).forEach(([lineId, line]) => {
      let shouldShow = true;
      
      // 按线路名称过滤
      if (criteria.lineName && !line.name.includes(criteria.lineName)) {
        shouldShow = false;
      }
      
      // 按是否有换乘站过滤
      if (criteria.hasTransfer !== undefined) {
        const hasTransferStation = line.stations.some(station => station.isTransfer);
        if (criteria.hasTransfer !== hasTransferStation) {
          shouldShow = false;
        }
      }
      
      // 按线路状态过滤
      if (criteria.disabled !== undefined && criteria.disabled !== line.disabled) {
        shouldShow = false;
      }
      
      if (shouldShow) {
        visibleLines.add(lineId);
      }
    });
    
    this.currentState.visibleLines = visibleLines;
    this.render();
    
    this.emit('linesFiltered', { criteria, visibleLines: Array.from(visibleLines) });
  }

  /**
   * 显示/隐藏线路
   * @param {string} lineId - 线路ID
   * @param {boolean} visible - 是否可见
   */
  toggleLineVisibility(lineId, visible) {
    if (visible) {
      this.currentState.visibleLines.add(lineId);
    } else {
      this.currentState.visibleLines.delete(lineId);
    }
    
    this.render();
    this.emit('lineVisibilityChanged', { lineId, visible });
  }

  /**
   * 高亮换乘线路
   * @param {string} stationId - 站点ID
   */
  highlightTransferLines(stationId) {
    const stationInfo = findStationById(stationId);
    
    if (stationInfo.length > 1) {
      // 这是一个换乘站，高亮所有相关线路
      stationInfo.forEach(info => {
        this.renderer.selectLine(info.line.id);
      });
    }
  }

  /**
   * 高亮线路站点
   * @param {string} lineId - 线路ID
   */
  highlightLineStations(lineId) {
    const line = this.metroData[lineId];
    if (line) {
      // 可以在这里实现高亮线路所有站点的逻辑
      this.emit('lineStationsHighlighted', { lineId, stations: line.stations });
    }
  }

  /**
   * 显示站点提示框
   * @param {string} stationId - 站点ID
   */
  showStationTooltip(stationId) {
    const stationInfo = findStationById(stationId);
    if (stationInfo.length > 0) {
      const tooltipData = {
        stationId,
        name: stationInfo[0].station.name,
        lines: stationInfo.map(info => ({
          id: info.line.id,
          name: info.line.name,
          color: info.line.colors.primary
        })),
        isTransfer: stationInfo.length > 1
      };
      
      this.emit('showTooltip', tooltipData);
    }
  }

  /**
   * 隐藏站点提示框
   */
  hideStationTooltip() {
    this.emit('hideTooltip');
  }

  /**
   * 更新信息面板
   * @param {string} stationId - 站点ID
   */
  updateInfoPanel(stationId) {
    const stationInfo = findStationById(stationId);
    if (stationInfo.length > 0) {
      const panelData = {
        station: stationInfo[0].station,
        lines: stationInfo.map(info => info.line),
        isTransfer: stationInfo.length > 1,
        transferLines: stationInfo.length > 1 ? stationInfo.map(info => info.line.name) : []
      };
      
      this.emit('updateInfoPanel', panelData);
    }
  }

  /**
   * 创建搜索UI
   */
  createSearchUI() {
    // 这里可以创建搜索输入框和结果显示
    // 具体实现可以根据需要定制
    this.emit('createSearchUI');
  }

  /**
   * 创建过滤UI
   */
  createFilterUI() {
    // 这里可以创建过滤控件
    this.emit('createFilterUI');
  }

  /**
   * 创建信息面板
   */
  createInfoPanel() {
    // 这里可以创建信息显示面板
    this.emit('createInfoPanel');
  }

  /**
   * 获取当前状态
   * @returns {Object} 当前状态
   */
  getState() {
    return { ...this.currentState };
  }

  /**
   * 获取所有换乘站
   * @returns {Array} 换乘站列表
   */
  getTransferStations() {
    return getTransferStations();
  }

  /**
   * 获取线路信息
   * @param {string} lineId - 线路ID
   * @returns {Object|null} 线路信息
   */
  getLineInfo(lineId) {
    return this.metroData[lineId] || null;
  }

  /**
   * 重置状态
   */
  reset() {
    this.currentState = {
      selectedStation: null,
      selectedLine: null,
      hoveredStation: null,
      visibleLines: new Set(Object.keys(this.metroData)),
      searchResults: [],
      filterCriteria: {}
    };
    
    this.render();
    this.emit('reset');
  }

  /**
   * 添加事件监听器
   * @param {string} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(eventName, callback) {
    if (!this.eventCallbacks.has(eventName)) {
      this.eventCallbacks.set(eventName, []);
    }
    this.eventCallbacks.get(eventName).push(callback);
  }

  /**
   * 移除事件监听器
   * @param {string} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(eventName, callback) {
    const callbacks = this.eventCallbacks.get(eventName) || [];
    const index = callbacks.indexOf(callback);
    if (index > -1) {
      callbacks.splice(index, 1);
    }
  }

  /**
   * 触发事件
   * @param {string} eventName - 事件名称
   * @param {*} data - 事件数据
   */
  emit(eventName, data) {
    const callbacks = this.eventCallbacks.get(eventName) || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in event callback for ${eventName}:`, error);
      }
    });
  }

  /**
   * 销毁控制器
   */
  destroy() {
    if (this.renderer) {
      this.renderer.destroy();
    }
    
    this.eventCallbacks.clear();
    this.emit('destroyed');
  }
}
