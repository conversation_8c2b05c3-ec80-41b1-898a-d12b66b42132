/**
 * 地铁线路图渲染引擎
 * @fileoverview 负责将地铁数据渲染为SVG图形
 */

import { DEFAULT_METRO_CONFIG } from '../config/metro-config.js';
import { calculateDistance, isPointInCircle } from '../utils/geometry.js';
import { blendColors, getContrastColor } from '../utils/color.js';

/**
 * 地铁线路图渲染器
 */
export class MetroRenderer {
  /**
   * 构造函数
   * @param {HTMLElement} container - 容器元素
   * @param {Object} config - 配置对象
   */
  constructor(container, config = {}) {
    this.container = container;
    this.config = { ...DEFAULT_METRO_CONFIG, ...config };
    this.svg = null;
    this.metroData = null;
    this.selectedStation = null;
    this.hoveredStation = null;
    this.selectedLine = null;
    this.eventListeners = new Map();
    
    this.init();
  }

  /**
   * 初始化渲染器
   */
  init() {
    this.createSVG();
    this.setupEventListeners();
  }

  /**
   * 创建SVG元素
   */
  createSVG() {
    this.svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    this.svg.setAttribute('width', this.config.canvas.width);
    this.svg.setAttribute('height', this.config.canvas.height);
    this.svg.setAttribute('viewBox', `0 0 ${this.config.canvas.width} ${this.config.canvas.height}`);
    this.svg.style.backgroundColor = this.config.canvas.backgroundColor;
    this.svg.style.cursor = 'grab';
    
    // 添加样式
    const style = document.createElementNS('http://www.w3.org/2000/svg', 'style');
    style.textContent = this.generateCSS();
    this.svg.appendChild(style);
    
    // 创建图层组
    this.createLayers();
    
    this.container.appendChild(this.svg);
  }

  /**
   * 创建图层组
   */
  createLayers() {
    // 线路图层
    this.linesLayer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    this.linesLayer.setAttribute('class', 'lines-layer');
    this.svg.appendChild(this.linesLayer);
    
    // 站点图层
    this.stationsLayer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    this.stationsLayer.setAttribute('class', 'stations-layer');
    this.svg.appendChild(this.stationsLayer);
    
    // 文本图层
    this.textLayer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    this.textLayer.setAttribute('class', 'text-layer');
    this.svg.appendChild(this.textLayer);
    
    // 标签图层
    this.labelsLayer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    this.labelsLayer.setAttribute('class', 'labels-layer');
    this.svg.appendChild(this.labelsLayer);
  }

  /**
   * 生成CSS样式
   * @returns {string} CSS字符串
   */
  generateCSS() {
    return `
      .metro-line {
        stroke-width: ${this.config.line.strokeWidth};
        fill: none;
        transition: stroke-width 0.2s ease;
      }
      .metro-line:hover {
        stroke-width: ${this.config.line.hoverStrokeWidth};
      }
      .metro-line.selected {
        stroke-width: ${this.config.line.selectedStrokeWidth};
      }
      .metro-station {
        stroke-width: ${this.config.station.strokeWidth};
        fill: white;
        cursor: pointer;
        transition: r 0.2s ease;
      }
      .metro-station:hover {
        r: ${this.config.station.hoverRadius};
      }
      .metro-station.selected {
        r: ${this.config.station.selectedRadius};
      }
      .station-text {
        font-family: ${this.config.text.fontFamily};
        font-size: ${this.config.text.fontSize}px;
        font-weight: ${this.config.text.fontWeight};
        fill: ${this.config.text.color};
        text-anchor: middle;
        dominant-baseline: middle;
        pointer-events: none;
        user-select: none;
      }
      .line-label {
        font-family: ${this.config.text.fontFamily};
        font-size: ${this.config.text.fontSize + 2}px;
        font-weight: bold;
        text-anchor: middle;
        dominant-baseline: middle;
        pointer-events: none;
        user-select: none;
      }
    `;
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    if (this.config.interaction.enableClick) {
      this.svg.addEventListener('click', this.handleClick.bind(this));
    }
    
    if (this.config.interaction.enableHover) {
      this.svg.addEventListener('mouseover', this.handleMouseOver.bind(this));
      this.svg.addEventListener('mouseout', this.handleMouseOut.bind(this));
    }
  }

  /**
   * 渲染地铁线路图
   * @param {Object} metroData - 地铁数据
   */
  render(metroData) {
    this.metroData = metroData;
    this.clear();
    
    // 渲染线路
    Object.values(metroData).forEach(line => {
      if (!line.disabled) {
        this.renderLine(line);
      }
    });
    
    // 渲染站点
    Object.values(metroData).forEach(line => {
      if (!line.disabled) {
        this.renderStations(line);
      }
    });
    
    // 渲染文本
    Object.values(metroData).forEach(line => {
      if (!line.disabled) {
        this.renderStationTexts(line);
        this.renderLineLabels(line);
      }
    });
  }

  /**
   * 清空画布
   */
  clear() {
    this.linesLayer.innerHTML = '';
    this.stationsLayer.innerHTML = '';
    this.textLayer.innerHTML = '';
    this.labelsLayer.innerHTML = '';
  }

  /**
   * 渲染线路
   * @param {Object} line - 线路数据
   */
  renderLine(line) {
    if (line.stations.length < 2) return;
    
    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path.setAttribute('class', 'metro-line');
    path.setAttribute('data-line-id', line.id);
    path.setAttribute('stroke', line.colors.primary);
    path.setAttribute('d', this.generateLinePath(line.stations));
    
    this.linesLayer.appendChild(path);
  }

  /**
   * 生成线路路径
   * @param {Array} stations - 站点数组
   * @returns {string} SVG路径字符串
   */
  generateLinePath(stations) {
    if (stations.length === 0) return '';
    
    let path = `M ${stations[0].position.x} ${stations[0].position.y}`;
    
    for (let i = 1; i < stations.length; i++) {
      const station = stations[i];
      path += ` L ${station.position.x} ${station.position.y}`;
    }
    
    return path;
  }

  /**
   * 渲染站点
   * @param {Object} line - 线路数据
   */
  renderStations(line) {
    line.stations.forEach(station => {
      if (station.name || station.id) { // 只渲染有名称或ID的站点
        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle.setAttribute('class', 'metro-station');
        circle.setAttribute('data-station-id', station.id);
        circle.setAttribute('data-line-id', line.id);
        circle.setAttribute('cx', station.position.x);
        circle.setAttribute('cy', station.position.y);
        circle.setAttribute('r', this.config.station.radius);
        circle.setAttribute('stroke', station.colors.primary);
        circle.setAttribute('fill', 'white');
        
        if (station.isTransfer) {
          circle.setAttribute('stroke-width', this.config.station.strokeWidth + 1);
        }
        
        this.stationsLayer.appendChild(circle);
      }
    });
  }

  /**
   * 渲染站点文本
   * @param {Object} line - 线路数据
   */
  renderStationTexts(line) {
    line.stations.forEach(station => {
      if (station.name) {
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('class', 'station-text');
        text.setAttribute('data-station-id', station.id);
        text.setAttribute('x', station.textPosition.x);
        text.setAttribute('y', station.textPosition.y);
        text.textContent = station.name;
        
        this.textLayer.appendChild(text);
      }
    });
  }

  /**
   * 渲染线路标签
   * @param {Object} line - 线路数据
   */
  renderLineLabels(line) {
    // 起点标签
    if (line.startLabel.x !== 0 || line.startLabel.y !== 0) {
      const startLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      startLabel.setAttribute('class', 'line-label');
      startLabel.setAttribute('x', line.startLabel.x);
      startLabel.setAttribute('y', line.startLabel.y);
      startLabel.setAttribute('fill', line.colors.primary);
      startLabel.textContent = line.name;
      
      this.labelsLayer.appendChild(startLabel);
    }
    
    // 终点标签
    if (line.endLabel.x !== 0 || line.endLabel.y !== 0) {
      const endLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      endLabel.setAttribute('class', 'line-label');
      endLabel.setAttribute('x', line.endLabel.x);
      endLabel.setAttribute('y', line.endLabel.y);
      endLabel.setAttribute('fill', line.colors.primary);
      endLabel.textContent = line.name;
      
      this.labelsLayer.appendChild(endLabel);
    }
  }

  /**
   * 处理点击事件
   * @param {Event} event - 点击事件
   */
  handleClick(event) {
    const target = event.target;
    
    if (target.classList.contains('metro-station')) {
      const stationId = target.getAttribute('data-station-id');
      const lineId = target.getAttribute('data-line-id');
      this.selectStation(stationId, lineId);
    } else if (target.classList.contains('metro-line')) {
      const lineId = target.getAttribute('data-line-id');
      this.selectLine(lineId);
    }
  }

  /**
   * 处理鼠标悬停事件
   * @param {Event} event - 鼠标事件
   */
  handleMouseOver(event) {
    const target = event.target;
    
    if (target.classList.contains('metro-station')) {
      const stationId = target.getAttribute('data-station-id');
      this.hoverStation(stationId);
    }
  }

  /**
   * 处理鼠标离开事件
   * @param {Event} event - 鼠标事件
   */
  handleMouseOut(event) {
    this.hoverStation(null);
  }

  /**
   * 选择站点
   * @param {string} stationId - 站点ID
   * @param {string} lineId - 线路ID
   */
  selectStation(stationId, lineId) {
    // 清除之前的选择
    this.svg.querySelectorAll('.metro-station.selected').forEach(el => {
      el.classList.remove('selected');
    });
    
    // 选择新站点
    const stationElement = this.svg.querySelector(`[data-station-id="${stationId}"][data-line-id="${lineId}"]`);
    if (stationElement) {
      stationElement.classList.add('selected');
      this.selectedStation = { stationId, lineId };
      
      // 触发事件
      this.emit('stationSelected', { stationId, lineId });
    }
  }

  /**
   * 悬停站点
   * @param {string} stationId - 站点ID
   */
  hoverStation(stationId) {
    // 清除之前的悬停状态
    this.svg.querySelectorAll('.metro-station.hovered').forEach(el => {
      el.classList.remove('hovered');
    });
    
    if (stationId) {
      // 设置新的悬停状态
      this.svg.querySelectorAll(`[data-station-id="${stationId}"]`).forEach(el => {
        el.classList.add('hovered');
      });
      
      this.hoveredStation = stationId;
      this.emit('stationHovered', { stationId });
    } else {
      this.hoveredStation = null;
      this.emit('stationHovered', { stationId: null });
    }
  }

  /**
   * 选择线路
   * @param {string} lineId - 线路ID
   */
  selectLine(lineId) {
    // 清除之前的选择
    this.svg.querySelectorAll('.metro-line.selected').forEach(el => {
      el.classList.remove('selected');
    });
    
    // 选择新线路
    const lineElement = this.svg.querySelector(`[data-line-id="${lineId}"]`);
    if (lineElement) {
      lineElement.classList.add('selected');
      this.selectedLine = lineId;
      
      // 触发事件
      this.emit('lineSelected', { lineId });
    }
  }

  /**
   * 触发自定义事件
   * @param {string} eventName - 事件名称
   * @param {Object} data - 事件数据
   */
  emit(eventName, data) {
    const listeners = this.eventListeners.get(eventName) || [];
    listeners.forEach(listener => listener(data));
  }

  /**
   * 添加事件监听器
   * @param {string} eventName - 事件名称
   * @param {Function} listener - 监听器函数
   */
  on(eventName, listener) {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, []);
    }
    this.eventListeners.get(eventName).push(listener);
  }

  /**
   * 移除事件监听器
   * @param {string} eventName - 事件名称
   * @param {Function} listener - 监听器函数
   */
  off(eventName, listener) {
    const listeners = this.eventListeners.get(eventName) || [];
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    this.eventListeners.clear();
    if (this.svg && this.svg.parentNode) {
      this.svg.parentNode.removeChild(this.svg);
    }
  }
}
