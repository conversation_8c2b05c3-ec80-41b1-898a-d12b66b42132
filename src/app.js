/**
 * 地铁线路图主应用程序
 * @fileoverview 应用程序入口点，负责初始化和协调各个组件
 */

import { MetroController } from './components/MetroController.js';
import { getAllLines } from './data/metro-lines.js';
import { getResponsiveConfig } from './config/metro-config.js';

/**
 * 地铁线路图应用程序类
 */
class MetroApp {
  constructor() {
    this.controller = null;
    this.elements = {};
    this.state = {
      isFullscreen: false,
      currentTooltip: null
    };
    
    this.init();
  }

  /**
   * 初始化应用程序
   */
  async init() {
    try {
      // 获取DOM元素
      this.getElements();
      
      // 设置事件监听器
      this.setupEventListeners();
      
      // 获取响应式配置
      const config = getResponsiveConfig(window.innerWidth);
      
      // 初始化地铁控制器
      this.controller = new MetroController(this.elements.metroCanvas, {
        rendererConfig: config,
        enableSearch: true,
        enableFilter: true,
        enableInfo: true
      });
      
      // 设置控制器事件监听
      this.setupControllerEvents();
      
      // 初始化UI组件
      this.initializeUI();
      
      // 隐藏加载指示器
      this.hideLoading();
      
      console.log('Metro app initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize metro app:', error);
      this.showError('应用程序初始化失败，请刷新页面重试。');
    }
  }

  /**
   * 获取DOM元素引用
   */
  getElements() {
    this.elements = {
      // 主要容器
      metroCanvas: document.getElementById('metroCanvas'),
      
      // 控制按钮
      resetBtn: document.getElementById('resetBtn'),
      fullscreenBtn: document.getElementById('fullscreenBtn'),
      zoomInBtn: document.getElementById('zoomInBtn'),
      zoomOutBtn: document.getElementById('zoomOutBtn'),
      fitBtn: document.getElementById('fitBtn'),
      
      // 搜索相关
      stationSearch: document.getElementById('stationSearch'),
      searchBtn: document.getElementById('searchBtn'),
      searchResults: document.getElementById('searchResults'),
      
      // 过滤相关
      lineFilters: document.getElementById('lineFilters'),
      
      // 信息面板
      infoPanel: document.getElementById('infoPanel'),
      infoContent: document.getElementById('infoContent'),
      closePanelBtn: document.getElementById('closePanelBtn'),
      
      // 提示框
      tooltip: document.getElementById('tooltip'),
      tooltipContent: document.getElementById('tooltipContent'),
      
      // 加载指示器
      loadingOverlay: document.getElementById('loadingOverlay')
    };
    
    // 验证必需元素
    const requiredElements = ['metroCanvas', 'resetBtn', 'stationSearch'];
    for (const elementId of requiredElements) {
      if (!this.elements[elementId]) {
        throw new Error(`Required element not found: ${elementId}`);
      }
    }
  }

  /**
   * 设置基础事件监听器
   */
  setupEventListeners() {
    // 重置按钮
    this.elements.resetBtn?.addEventListener('click', () => {
      this.controller?.reset();
      this.hideInfoPanel();
    });
    
    // 全屏按钮
    this.elements.fullscreenBtn?.addEventListener('click', () => {
      this.toggleFullscreen();
    });
    
    // 搜索功能
    this.elements.searchBtn?.addEventListener('click', () => {
      this.performSearch();
    });
    
    this.elements.stationSearch?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.performSearch();
      }
    });
    
    this.elements.stationSearch?.addEventListener('input', (e) => {
      if (e.target.value.trim() === '') {
        this.clearSearchResults();
      }
    });
    
    // 信息面板关闭按钮
    this.elements.closePanelBtn?.addEventListener('click', () => {
      this.hideInfoPanel();
    });
    
    // 窗口大小变化
    window.addEventListener('resize', () => {
      this.handleResize();
    });
    
    // 全屏状态变化
    document.addEventListener('fullscreenchange', () => {
      this.handleFullscreenChange();
    });
  }

  /**
   * 设置控制器事件监听
   */
  setupControllerEvents() {
    if (!this.controller) return;
    
    // 站点选择事件
    this.controller.on('stationSelected', (data) => {
      this.handleStationSelected(data);
    });
    
    // 站点悬停事件
    this.controller.on('stationHovered', (data) => {
      this.handleStationHovered(data);
    });
    
    // 线路选择事件
    this.controller.on('lineSelected', (data) => {
      this.handleLineSelected(data);
    });
    
    // 搜索结果事件
    this.controller.on('searchResults', (data) => {
      this.displaySearchResults(data.results);
    });
    
    // 错误事件
    this.controller.on('error', (data) => {
      this.showError(data.error.message || '发生未知错误');
    });
  }

  /**
   * 初始化UI组件
   */
  initializeUI() {
    this.createLineFilters();
  }

  /**
   * 创建线路过滤器
   */
  createLineFilters() {
    if (!this.elements.lineFilters) return;
    
    const lines = getAllLines();
    const fragment = document.createDocumentFragment();
    
    Object.values(lines).forEach(line => {
      const filterItem = document.createElement('div');
      filterItem.className = 'line-filter-item';
      
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.id = `line-${line.id}`;
      checkbox.className = 'line-filter-checkbox';
      checkbox.checked = true;
      checkbox.addEventListener('change', (e) => {
        this.controller?.toggleLineVisibility(line.id, e.target.checked);
      });
      
      const colorBox = document.createElement('div');
      colorBox.className = 'line-filter-color';
      colorBox.style.backgroundColor = line.colors.primary;
      
      const label = document.createElement('label');
      label.htmlFor = `line-${line.id}`;
      label.className = 'line-filter-name';
      label.textContent = line.name;
      
      filterItem.appendChild(checkbox);
      filterItem.appendChild(colorBox);
      filterItem.appendChild(label);
      
      fragment.appendChild(filterItem);
    });
    
    this.elements.lineFilters.appendChild(fragment);
  }

  /**
   * 执行搜索
   */
  performSearch() {
    const query = this.elements.stationSearch?.value?.trim();
    if (!query) {
      this.clearSearchResults();
      return;
    }
    
    const results = this.controller?.searchStations(query) || [];
    this.displaySearchResults(results);
  }

  /**
   * 显示搜索结果
   * @param {Array} results - 搜索结果
   */
  displaySearchResults(results) {
    if (!this.elements.searchResults) return;
    
    this.elements.searchResults.innerHTML = '';
    
    if (results.length === 0) {
      const noResults = document.createElement('div');
      noResults.className = 'search-no-results';
      noResults.textContent = '未找到匹配的站点';
      this.elements.searchResults.appendChild(noResults);
      return;
    }
    
    results.forEach(result => {
      const item = document.createElement('div');
      item.className = 'search-result-item';
      item.addEventListener('click', () => {
        this.controller?.selectStation(result.station.id, result.line.id);
      });
      
      const stationName = document.createElement('div');
      stationName.className = 'search-result-station';
      stationName.textContent = result.station.name;
      
      const lineName = document.createElement('div');
      lineName.className = 'search-result-line';
      lineName.textContent = result.line.name;
      lineName.style.color = result.line.colors.primary;
      
      item.appendChild(stationName);
      item.appendChild(lineName);
      
      this.elements.searchResults.appendChild(item);
    });
  }

  /**
   * 清空搜索结果
   */
  clearSearchResults() {
    if (this.elements.searchResults) {
      this.elements.searchResults.innerHTML = '';
    }
  }

  /**
   * 处理站点选择
   * @param {Object} data - 站点数据
   */
  handleStationSelected(data) {
    const stationInfo = this.controller?.findStationById(data.stationId);
    if (stationInfo && stationInfo.length > 0) {
      this.showInfoPanel(stationInfo[0]);
    }
  }

  /**
   * 处理站点悬停
   * @param {Object} data - 悬停数据
   */
  handleStationHovered(data) {
    if (data.stationId) {
      this.showTooltip(data.stationId);
    } else {
      this.hideTooltip();
    }
  }

  /**
   * 处理线路选择
   * @param {Object} data - 线路数据
   */
  handleLineSelected(data) {
    console.log('Line selected:', data.lineId);
  }

  /**
   * 显示信息面板
   * @param {Object} stationInfo - 站点信息
   */
  showInfoPanel(stationInfo) {
    if (!this.elements.infoPanel || !this.elements.infoContent) return;
    
    const { station, line } = stationInfo;
    
    const content = `
      <div class="station-info">
        <h4>${station.name}</h4>
        <div class="station-details">
          <div class="detail-item">
            <span class="detail-label">所属线路:</span>
            <span class="detail-value" style="color: ${line.colors.primary}">${line.name}</span>
          </div>
          ${station.isTransfer ? '<div class="detail-item"><span class="badge badge-primary">换乘站</span></div>' : ''}
          ${station.isTerminal ? '<div class="detail-item"><span class="badge badge-secondary">终点站</span></div>' : ''}
        </div>
      </div>
    `;
    
    this.elements.infoContent.innerHTML = content;
    this.elements.infoPanel.classList.add('show');
  }

  /**
   * 隐藏信息面板
   */
  hideInfoPanel() {
    if (this.elements.infoPanel) {
      this.elements.infoPanel.classList.remove('show');
    }
  }

  /**
   * 显示提示框
   * @param {string} stationId - 站点ID
   */
  showTooltip(stationId) {
    // 提示框功能的具体实现
    // 这里可以根据需要添加更详细的提示信息
  }

  /**
   * 隐藏提示框
   */
  hideTooltip() {
    if (this.elements.tooltip) {
      this.elements.tooltip.classList.remove('show');
    }
  }

  /**
   * 切换全屏模式
   */
  toggleFullscreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  }

  /**
   * 处理全屏状态变化
   */
  handleFullscreenChange() {
    this.state.isFullscreen = !!document.fullscreenElement;
    const btn = this.elements.fullscreenBtn;
    if (btn) {
      btn.textContent = this.state.isFullscreen ? '退出全屏' : '全屏';
    }
  }

  /**
   * 处理窗口大小变化
   */
  handleResize() {
    // 可以在这里添加响应式处理逻辑
    const config = getResponsiveConfig(window.innerWidth);
    // 更新渲染器配置
  }

  /**
   * 显示错误信息
   * @param {string} message - 错误信息
   */
  showError(message) {
    console.error('Metro App Error:', message);
    // 这里可以添加用户友好的错误显示
    alert(message);
  }

  /**
   * 隐藏加载指示器
   */
  hideLoading() {
    if (this.elements.loadingOverlay) {
      this.elements.loadingOverlay.classList.add('hide');
      setTimeout(() => {
        this.elements.loadingOverlay.style.display = 'none';
      }, 300);
    }
  }
}

// 当DOM加载完成后初始化应用程序
document.addEventListener('DOMContentLoaded', () => {
  new MetroApp();
});
