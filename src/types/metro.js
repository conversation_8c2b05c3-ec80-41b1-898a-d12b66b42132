/**
 * 地铁线路图数据类型定义
 * @fileoverview 定义地铁线路、站点等核心数据结构
 */

/**
 * 位置坐标
 * @typedef {Object} Position
 * @property {number} x - X坐标
 * @property {number} y - Y坐标
 */

/**
 * 颜色配置
 * @typedef {Object} ColorConfig
 * @property {string} primary - 主要颜色
 * @property {string} secondary - 次要颜色
 * @property {string} hidden - 隐藏状态颜色
 * @property {string} active - 激活状态颜色
 */

/**
 * 地铁站点
 * @typedef {Object} Station
 * @property {string} id - 站点唯一标识符
 * @property {string} name - 站点名称
 * @property {Position} position - 站点位置坐标
 * @property {Position} textPosition - 文本显示位置
 * @property {ColorConfig} colors - 颜色配置
 * @property {boolean} isTransfer - 是否为换乘站
 * @property {boolean} isTerminal - 是否为终点站
 * @property {boolean} isCorner - 是否为转角站点
 * @property {string[]} transferLines - 换乘线路ID列表
 * @property {boolean} disabled - 是否禁用
 * @property {Object} metadata - 额外元数据
 */

/**
 * 地铁线路
 * @typedef {Object} MetroLine
 * @property {string} id - 线路唯一标识符
 * @property {string} name - 线路名称
 * @property {ColorConfig} colors - 线路颜色配置
 * @property {Position} startLabel - 起点标签位置
 * @property {Position} endLabel - 终点标签位置
 * @property {Station[]} stations - 站点列表
 * @property {boolean} disabled - 是否禁用
 * @property {Object} metadata - 额外元数据
 */

/**
 * 地铁系统配置
 * @typedef {Object} MetroConfig
 * @property {Object} canvas - 画布配置
 * @property {number} canvas.width - 画布宽度
 * @property {number} canvas.height - 画布高度
 * @property {Object} station - 站点样式配置
 * @property {number} station.radius - 站点圆半径
 * @property {number} station.strokeWidth - 边框宽度
 * @property {Object} line - 线路样式配置
 * @property {number} line.strokeWidth - 线路宽度
 * @property {Object} text - 文本样式配置
 * @property {string} text.fontFamily - 字体
 * @property {number} text.fontSize - 字体大小
 * @property {Object} animation - 动画配置
 * @property {number} animation.duration - 动画持续时间
 * @property {string} animation.easing - 缓动函数
 */

// 导出类型定义（用于JSDoc）
export const MetroTypes = {
  Position: {},
  ColorConfig: {},
  Station: {},
  MetroLine: {},
  MetroConfig: {}
};
