/**
 *   '01': {
 *     name: '1号线', // 线路名称
 *     px: 1, // 线路起点文本描述x
 *     py: 205, // 线路起点文本描述y
 *     ex: 0, // 线路终点文本描述x (0代表当前描述不显示)
 *     ey: 405, // 线路终点文本描述y
 *     color: '#00A3E9', // 线路基础颜色
 *     hideColor: '#CBE1F2', // 隐藏颜色
 *     showColor: '#00A3E9', // 激活颜色 同 基础颜色
 *     data: [
 *       {
 *         px: 20, // 站点圆的x
 *         py: 250, // 站点圆的y
 *         name: '咸阳西站', // 站点文本
 *         hideColor: '#CBE1F2', // 站点圆边框隐藏颜色
 *         showColor: '#00A3E9', // 站点圆边框基础颜色
 *         textpx: 30, // 站点文本x
 *         textpy: 250, // 站点文本y
 *         stationId: '0106', // 站点id
 *         corner: true, // 是否圆角, 暂时不用
 *         firstStation: true // 是否是第一个站, 暂时不用
 *         transfer: true // 是否是换乘站
 *       },
 *      ]
 *     }
 */
const lineData = {
  // 1号线数据
  '01': {
    name: '1号线',
    px: 1,
    py: 205,
    ex: 0,
    ey: 405,
    color: '#00A3E9',
    hideColor: '#CBE1F2', // 线和圆的隐藏颜色
    showColor: '#00A3E9', // 线和圆的显示颜色
    data: [{
      px: 20,
      py: 250,
      name: '咸阳西站',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 30, // 文字位置
      textpy: 250,
      stationId: '0106',
      corner: true,
      firstStation: true
    }, {
      px: 20,
      py: 290,
      name: '宝泉路',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 30, // 文字位置
      textpy: 290,
      stationId: '0107'
    }, {
      px: 20,
      py: 330,
      name: '中华西路',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 30, // 文字位置
      textpy: 330,
      stationId: '0108',
      corner: true,
      firstStation: true
    }, {
      px: 20,
      py: 370,
      name: '',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 330, // 文字位置
      textpy: 386,
      stationId: '0108G0109'
    }, {
      px: 40,
      py: 370,
      name: '安谷',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 25, // 文字位置
      textpy: 385,
      stationId: '0109',
      corner: true
    }, {
      px: 80,
      py: 370,
      name: '陈杨寨',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 65, // 文字位置
      textpy: 385,
      stationId: '0110'
    }, {
      px: 120,
      py: 370,
      name: '白马河',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 105, // 文字位置
      textpy: 385,
      stationId: '0111'
    }, {
      px: 150,
      py: 370,
      name: '陕西中医药大学',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 120, // 文字位置
      textpy: 355,
      stationId: '0112'
    }, {
      px: 190,
      py: 370,
      name: '沣河森林公园',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 160, // 文字位置
      textpy: 385,
      stationId: '0113'
    }, {
      px: 220,
      py: 370,
      name: '北槐',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 210, // 文字位置
      textpy: 355,
      stationId: '0115'
    }, {
      px: 250,
      py: 370,
      name: '上林路',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 255, // 文字位置
      textpy: 390,
      stationId: ['1633', '0117'],
      transfer: true
    }, {
      px: 280,
      py: 370,
      name: '沣东自贸园',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 260, // 文字位置
      textpy: 355,
      stationId: '0119'
    }, {
      px: 310,
      py: 370,
      name: '后卫寨',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 320, // 文字位置
      textpy: 370,
      stationId: '0121',
      corner: true,
      firstStation: true
    }, {
      px: 330,
      py: 447,
      name: '三桥',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 335, // 文字位置
      textpy: 435,
      stationId: '0123',
      corner: true
    }, {
      px: 360,
      py: 447,
      name: '皂河',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 335, // 文字位置
      textpy: 465,
      stationId: '0125'
    }, {
      px: 390,
      py: 447,
      name: '枣园',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 365, // 文字位置
      textpy: 435,
      stationId: '0127'
    }, {
      px: 410,
      py: 447,
      name: '汉城路',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 395, // 文字位置
      textpy: 465,
      stationId: '0129'
    }, {
      px: 440,
      py: 447,
      name: '开远门',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 455 - 28 - 30, // 文字位置
      textpy: 435 - 5,
      stationId: '0131'
    }, {
      px: 500,
      py: 447,
      name: '劳动路',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 485, // 文字位置
      textpy: 465,
      stationId: '0133'
    }, {
      px: 530,
      py: 447,
      name: '玉祥门',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 510, // 文字位置
      textpy: 435,
      stationId: '0135'
    }, {
      px: 560,
      py: 447,
      name: '洒金桥',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 548, // 文字位置
      textpy: 434,
      stationId: '0137'
    }, {
      px: 600,
      py: 446.5,
      name: '北大街',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 605, // 文字位置
      textpy: 435,
      stationId: ['0139', '0239'],
      transfer: true
    }, {
      px: 689,
      py: 446.5,
      name: '五路口',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 645, // 文字位置
      textpy: 435,
      stationId: ['0449', '0141'],
      transfer: true
    }, {
      px: 717,
      py: 447,
      name: '朝阳门',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 700, // 文字位置
      textpy: 435,
      stationId: '0143'
    }, {
      px: 779,
      py: 447,
      name: '康复路',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 759, // 文字位置
      textpy: 435,
      stationId: '0145'
    }, {
      px: 814,
      py: 446.5,
      name: '通化门',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 810, // 文字位置
      textpy: 435,
      stationId: ['0347', '0147'],
      transfer: true,
      showNameFlag: true
    }, {
      px: 860 + 15,
      py: 447,
      name: '万寿路',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 845 - 10, // 文字位置
      textpy: 460,
      stationId: '0149'
    }, {
      px: 890 + 15,
      py: 447,
      name: '长乐坡',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 875 + 15, // 文字位置
      textpy: 435,
      stationId: '0151'
    }, {
      px: 925,
      py: 447,
      name: '浐河',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 912, // 文字位置
      textpy: 465,
      stationId: '0153'
    }, {
      px: 950,
      py: 447,
      name: '半坡',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 940, // 文字位置
      textpy: 465,
      stationId: '0155'
    }, {
      px: 980,
      py: 447,
      name: '纺织城',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#00A3E9', // 线和圆的显示颜色
      textpx: 960, // 文字位置
      textpy: 434,
      stationId: ['0921', '0683', '0157'],
      transfer: true
    }]
  }, // 2号线数据
  '02': {
    name: '2号线',
    px: 590,
    py: -10,
    ex: 0,
    ey: 880,
    color: '#E62128',
    hideColor: '#E3CACA', // 线和圆的隐藏颜色
    showColor: '#E62128', // 线和圆的显示颜色
    data: [{
      px: 670,
      py: 40,
      name: '草滩',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 680, // 文字位置
      textpy: 40,
      stationId: '0217'
    }, {
      px: 630,
      py: 40,
      name: '红会医院北区',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 590, // 文字位置
      textpy: 25,
      stationId: '0219',
      corner: true,
      firstStation: true
    }, {
      px: 600,
      py: 40,
      name: '',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 610, // 文字位置
      textpy: 107,
      stationId: '0219G0221'
    }, {
      px: 599,
      py: 107,
      name: '西安北站',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 539, // 文字位置
      textpy: 107,
      stationId: ['1439', '0477', '0221'],
      transfer: true,
      corner: true
    }, {
      px: 600,
      py: 143,
      name: '北苑',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 610, // 文字位置
      textpy: 143,
      stationId: '0223'
    }, {
      px: 600,
      py: 173,
      name: '凤城十路',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 610, // 文字位置
      textpy: 173,
      stationId: '0225'
    }, {
      px: 600,
      py: 222.5,
      name: '行政中心',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 546, // 文字位置
      textpy: 213,
      stationId: ['0467', '0227'],
      transfer: true
    }, {
      px: 600,
      py: 273,
      name: '凤城五路',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 544, // 文字位置
      textpy: 273,
      stationId: '0229'
    }, {
      px: 600,
      py: 302,
      name: '市图书馆',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 544 + 50, // 文字位置
      textpy: 307 - 15,
      stationId: '0231'
    }, {
      px: 600,
      py: 339,
      name: '大明宫西',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 544, // 文字位置
      textpy: 339,
      stationId: '0233'
    }, {
      px: 600,
      py: 373,
      name: '龙首原',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 555, // 文字位置
      textpy: 373,
      stationId: '0235'
    }, {
      px: 600,
      py: 402,
      name: '安远门',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 555, // 文字位置
      textpy: 402,
      stationId: '0237'
    }, {
      px: 600,
      py: 446.5,
      name: '北大街',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 605, // 文字位置
      textpy: 435,
      stationId: ['0139', '0239'],
      transfer: true,
      showNameFlag: true
    }, {
      px: 600,
      py: 475,
      name: '钟楼',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 605, // 文字位置
      textpy: 485,
      stationId: ['0659', '0241'],
      transfer: true
    }, {
      px: 600,
      py: 501,
      name: '永宁门',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 610, // 文字位置
      textpy: 501,
      stationId: '0243'
    }, {
      px: 600,
      py: 535,
      name: '南稍门',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 575, // 文字位置
      textpy: 523,
      stationId: ['0565', '0245'],
      transfer: true
    }, {
      px: 600,
      py: 564,
      name: '体育场',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 610, // 文字位置
      textpy: 564,
      stationId: '0247'
    }, {
      px: 600,
      py: 596.5,
      name: '小寨',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 610, // 文字位置
      textpy: 612,
      stationId: ['0249', '0333'],
      transfer: true,
      showNameFlag: true
    }, {
      px: 600,
      py: 665 - 30,
      name: '纬一街',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 555, // 文字位置
      textpy: 665 - 30,
      stationId: '0251'
    }, {
      px: 600,
      py: 688,
      name: '电视塔',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 610, // 文字位置
      textpy: 732 - 15 - 44,
      stationId: '0253'
    }, {
      px: 600,
      py: 752 - 40,
      name: '三爻',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 567, // 文字位置
      textpy: 752 - 40 + 8,
      stationId: '0255'
    }, {
      px: 600,
      py: 777 - 35,
      name: '凤栖原',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 555, // 文字位置
      textpy: 777 - 35,
      stationId: '0257'
    }, {
      px: 600,
      py: 800 - 30,
      name: '航天城',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 555, // 文字位置
      textpy: 800 - 30,
      stationId: '0259'
    }, {
      px: 600,
      py: 822 - 25,
      name: '韦曲南',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 555, // 文字位置
      textpy: 822 - 25,
      stationId: '0261'
    }, {
      px: 600,
      py: 852 - 10,
      name: '何家营',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 555, // 文字位置
      textpy: 852 - 10,
      stationId: '0263'
    }, {
      px: 600,
      py: 880,
      name: '常宁宫',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#E62128', // 线和圆的显示颜色
      textpx: 555, // 文字位置
      textpy: 880,
      stationId: '0265'
    }]
  }, // 3号线数据
  '03': {
    name: '3号线',
    px: 870,
    py: 20,
    color: '#BC80A8',
    hideColor: '#DFD9E9', // 线和圆的隐藏颜色
    showColor: '#BC80A8', // 线和圆的显示颜色
    ex: 0,
    ey: 600,
    data: [{
      px: 320,
      py: 576,
      name: '鱼化寨',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 305, // 文字位置
      textpy: 590,
      stationId: '0321'
    }, {
      px: 390,
      py: 576,
      name: '丈八北路',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 365, // 文字位置
      textpy: 590,
      stationId: '0323'
    }, {
      px: 440,
      py: 576,
      name: '延平门',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 425 - 30, // 文字位置
      textpy: 590 - 25,
      stationId: '0325',
      corner: true,
      firstStation: true
    }, {
      px: 464,
      py: 576,
      name: '',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 288, // 文字位置
      textpy: 578,
      stationId: '0325G0327'
    }, {
      px: 502,
      py: 587,
      name: '科技路',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 493 + 20, // 文字位置
      textpy: 580,
      stationId: ['0647', '0327'],
      corner: true,
      transfer: true
    }, {
      px: 520,
      py: 597,
      name: '太白南路',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 490 + 15, // 文字位置
      textpy: 613,
      stationId: '0329'
    }, {
      px: 560,
      py: 597,
      name: '吉祥村',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 545 + 15, // 文字位置
      textpy: 613,
      stationId: '0331'
    }, {
      px: 600,
      py: 596.5,
      name: '小寨',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 610, // 文字位置
      textpy: 612,
      stationId: ['0249', '0333'],
      transfer: true
    }, {
      px: 689,
      py: 596,
      name: '大雁塔',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 645, // 文字位置
      textpy: 590,
      stationId: ['0439', '0335'],
      transfer: true
    }, {
      px: 741,
      py: 597,
      name: '北池头',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 723, // 文字位置
      textpy: 613,
      stationId: '0337'
    }, {
      px: 793,
      py: 597,
      name: '青龙寺',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 803, // 文字位置
      textpy: 597,
      stationId: ['0575', '0339'],
      corner: true,
      firstStation: true
    }, {
      px: 814,
      py: 581,
      name: '',
      startName: '青龙寺',
      endName: '延兴门',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 490, // 文字位置
      textpy: 620,
      stationId: '0339G0341'
    }, {
      px: 814,
      py: 565,
      name: '延兴门',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 825, // 文字位置
      textpy: 565,
      stationId: '0341',
      corner: true
    }, {
      px: 814,
      py: 525,
      name: '咸宁路',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 810, // 文字位置
      textpy: 538,
      stationId: ['0667', '0343'],
      transfer: true
    }, {
      px: 814,
      py: 490,
      name: '长乐公园',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 825, // 文字位置
      textpy: 490,
      stationId: '0345'
    }, {
      px: 814,
      py: 446.5,
      name: '通化门',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 810, // 文字位置
      textpy: 435,
      stationId: ['0347', '0147'],
      transfer: true
    }, {
      px: 813,
      py: 398,
      name: '胡家庙',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 820, // 文字位置
      textpy: 398,
      stationId: '0349'
    }, {
      px: 813,
      py: 369,
      name: '石家街',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 820, // 文字位置
      textpy: 369,
      stationId: '0351',
      corner: true,
      firstStation: true
    }, {
      px: 813,
      py: 347,
      name: '',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 590, // 文字位置
      textpy: 338,
      stationId: '0351G0353'
    }, {
      px: 826,
      py: 329,
      name: '辛家庙',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 808, // 文字位置
      textpy: 315,
      stationId: '0353',
      corner: true
    }, {
      px: 863 + 12,
      py: 329,
      name: '广泰门',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 848 - 12, // 文字位置
      textpy: 344,
      stationId: '0355'
    }, {
      px: 903,
      py: 329,
      name: '桃花潭',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 910, // 文字位置
      textpy: 335,
      stationId: '0357',
      corner: true,
      firstStation: true
    }, {
      px: 930,
      py: 310,
      name: '',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 930, // 文字位置
      textpy: 310,
      stationId: '0357G0359'
    }, {
      px: 930,
      py: 280,
      name: '浐灞中心',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 940, // 文字位置
      textpy: 280,
      stationId: '0359',
      corner: true
    }, {
      px: 930,
      py: 230,
      name: '香湖湾',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 940, // 文字位置
      textpy: 230,
      stationId: '0361'
    }, {
      px: 930,
      py: 180,
      name: '务庄',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 940, // 文字位置
      textpy: 180,
      stationId: '0363'
    }, {
      px: 930,
      py: 130,
      name: '国际港务区',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 940, // 文字位置
      textpy: 130,
      stationId: '0365'
    }, {
      px: 930,
      py: 90,
      name: '双寨',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 900, // 文字位置
      textpy: 80,
      stationId: ['1449', '0367'],
      transfer: true
    }, {
      px: 930,
      py: 60,
      name: '新筑',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 940, // 文字位置
      textpy: 60,
      stationId: '0369'
    }, {
      px: 930,
      py: 30,
      name: '保税区',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#BC80A8', // 线和圆的显示颜色
      textpx: 940, // 文字位置
      textpy: 36,
      stationId: '0371'
    }]
  }, // 4号线数据
  '04': {
    name: '4号线',
    px: 0,
    py: 60,
    color: '#02AF8E',
    hideColor: '#CEE4EA', // 线和圆的隐藏颜色
    showColor: '#02AF8E', // 线和圆的显示颜色
    ex: 900,
    ey: 870,
    data: [{
      px: 599,
      py: 90,
      name: '西安北站',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 539, // 文字位置
      textpy: 107,
      stationId: ['1439', '0477', '0221'],
      transfer: true,
      corner: true,
      firstStation: true
    }, {
      px: 544,
      py: 90,
      name: '',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 385, // 文字位置
      textpy: 70,
      stationId: '0477G0475'
    }, {
      px: 524,
      py: 101,
      name: '元朔路',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 480, // 文字位置
      textpy: 103,
      stationId: '0475',
      corner: true
    }, {
      px: 524,
      py: 150,
      name: '凤城十二路',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 457, // 文字位置
      textpy: 150,
      stationId: '0473'
    }, {
      px: 524,
      py: 188,
      name: '凤城九路',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 470, // 文字位置
      textpy: 188,
      stationId: '0471',
      corner: true,
      firstStation: true
    }, {
      px: 524,
      py: 204,
      name: '',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 388, // 文字位置
      textpy: 260,
      stationId: '0471G0469'
    }, {
      px: 544,
      py: 223,
      name: '文景路',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 525, // 文字位置
      textpy: 240,
      stationId: '0469',
      corner: true
    }, {
      px: 600,
      py: 222.5,
      name: '行政中心',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 546, // 文字位置
      textpy: 213,
      stationId: ['0467', '0227'],
      transfer: true,
      showNameFlag: true
    }, {
      px: 657,
      py: 223,
      name: '市中医医院',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 634, // 文字位置
      textpy: 208,
      stationId: '0465',
      firstStation: true,
      corner: true
    }, {
      px: 671,
      py: 223,
      name: '',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 634, // 文字位置
      textpy: 208,
      stationId: '0465G0463'
    }, {
      px: 689,
      py: 236,
      name: '常青路',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 645, // 文字位置
      textpy: 245,
      stationId: '0463',
      corner: true
    }, {
      px: 689,
      py: 273,
      name: '百花村',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 645, // 文字位置
      textpy: 273,
      stationId: '0461'
    }, {
      px: 689,
      py: 302,
      name: '余家寨',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 650, // 文字位置
      textpy: 302 - 10,
      stationId: '0459'
    }, {
      px: 689,
      py: 335,
      name: '大明宫北',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 335,
      stationId: '0457'
    }, {
      px: 689,
      py: 366,
      name: '大明宫',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 366,
      stationId: '0455'
    }, {
      px: 689,
      py: 388,
      name: '含元殿',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 388,
      stationId: '0453'
    }, {
      px: 689,
      py: 413,
      name: '西安站',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 413,
      stationId: '0451'
    }, {
      px: 689,
      py: 446.5,
      name: '五路口',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 645, // 文字位置
      textpy: 435,
      stationId: ['0449', '0141'],
      transfer: true,
      showNameFlag: true
    }, {
      px: 689,
      py: 475,
      name: '大差市',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 649, // 文字位置
      textpy: 465,
      stationId: ['0661', '0447'],
      transfer: true
    }, {
      px: 689,
      py: 495,
      name: '和平门',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 495,
      stationId: '0445'
    }, {
      px: 688,
      py: 534,
      name: '建筑科技大学·李家村',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 548,
      stationId: ['0569', '0443'],
      transfer: true
    }, {
      px: 689,
      py: 566,
      name: '西安科技大学',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 570,
      stationId: '0441'
    }, {
      px: 689,
      py: 596,
      name: '大雁塔',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 645, // 文字位置
      textpy: 590,
      stationId: ['0439', '0335'],
      transfer: true,
      showNameFlag: true
    }, {
      px: 689,
      py: 665 - 30,
      name: '大唐芙蓉园',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 665 - 30,
      stationId: '0437'
    }, {
      px: 689,
      py: 688,
      name: '曲江池西',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 688 - 15,
      stationId: '0435'
    }, {
      px: 689,
      py: 757 - 40,
      name: '金滹沱',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 757 - 40,
      stationId: '0433'
    }, {
      px: 689,
      py: 782 - 30,
      name: '航天大道',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 782 - 30,
      stationId: '0431'
    }, {
      px: 689,
      py: 800 - 20,
      name: '飞天路',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 800 - 20,
      stationId: '0429'
    }, {
      px: 689,
      py: 822 - 10,
      name: '东长安街',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 822 - 10,
      stationId: '0427',
      corner: true,
      firstStation: true
    }, {
      px: 689,
      py: 837,
      name: '',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 817,
      stationId: '0427G0425'
    }, {
      px: 710,
      py: 857,
      name: '神舟大道',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 690, // 文字位置
      textpy: 880,
      stationId: '0425',
      corner: true
    }, {
      px: 810,
      py: 858,
      name: '航天东路',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 789, // 文字位置
      textpy: 840,
      stationId: '0423'
    }, {
      px: 896,
      py: 858,
      name: '航天新城',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 877, // 文字位置
      textpy: 840,
      stationId: '0421'
    }]
  }, // 5号线数据
  '05': {
    name: '5号线',
    px: 1020,
    py: 650,
    color: '#C4D70D',
    hideColor: '#ddded0', // 线和圆的隐藏颜色
    showColor: '#C4D70D', // 线和圆的显示颜色
    ex: 0,
    ey: 550,
    data: [{
      px: 43,
      py: 532,
      name: '创新港',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 50, // 文字位置
      textpy: 532,
      stationId: '0521',
      firstStation: true
    }, {
      px: 43,
      py: 512,
      name: '创新港东',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 50, // 文字位置
      textpy: 512,
      stationId: '0523'
    }, {
      px: 43,
      py: 494,
      name: '翱翔小镇',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 10, // 文字位置
      textpy: 476,
      stationId: '0525'
    }, {
      px: 70,
      py: 494,
      name: '钓台',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 57, // 文字位置
      textpy: 476,
      stationId: '0527'
    }, {
      px: 100,
      py: 494,
      name: '沣西文化公园',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 88, // 文字位置
      textpy: 476,
      stationId: '0529'
    }, {
      px: 125,
      py: 494,
      name: '东马坊',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 135, // 文字位置
      textpy: 494,
      stationId: '0531'
    }, {
      px: 124,
      py: 534,
      name: '高桥',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 112, // 文字位置
      textpy: 548,
      stationId: '0533'
    }, {
      px: 160,
      py: 534,
      name: '文教园',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 145, // 文字位置
      textpy: 523,
      stationId: '0535'
    }, {
      px: 190,
      py: 534,
      name: '欢乐谷',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 152, // 文字位置
      textpy: 549,
      stationId: ['1623', '0537'],
      transfer: true
    }, {
      px: 220,
      py: 534,
      name: '镐京',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 210, // 文字位置
      textpy: 523,
      stationId: '0539'
    }, {
      px: 250,
      py: 534,
      name: '复兴大道南',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 221, // 文字位置
      textpy: 548,
      stationId: '0541',
      corner: true
    }, {
      px: 280,
      py: 534,
      name: '斗门',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 265, // 文字位置
      textpy: 523,
      stationId: '0543'
    }, {
      px: 310,
      py: 534,
      name: '王寺',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 285, // 文字位置
      textpy: 548,
      stationId: '0545'
    }, {
      px: 330,
      py: 534,
      name: '阿房宫南',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 310, // 文字位置
      textpy: 523,
      transfer: true,
      stationId: ['0547', 'xh01']
    }, {
      px: 350,
      py: 534,
      name: '石桥立交',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 330, // 文字位置
      textpy: 548,
      stationId: '0549'
    }, {
      px: 380,
      py: 534,
      name: '西窑头',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 360, // 文字位置
      textpy: 523,
      stationId: '0551'
    }, {
      px: 410,
      py: 534,
      name: '汉城南路',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 386, // 文字位置
      textpy: 548,
      stationId: '0553'
    }, {
      px: 440,
      py: 534,
      name: '金光门',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 425 - 25, // 文字位置
      textpy: 523 - 7,
      stationId: '0555'
    }, {
      px: 470,
      py: 534,
      name: '丰庆公园',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 448, // 文字位置
      textpy: 548,
      stationId: '0557'
    }, {
      px: 502,
      py: 534,
      name: '西北工业大学',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 475, // 文字位置
      textpy: 523,
      stationId: ['0649', '0559'],
      transfer: true
    }, {
      px: 530,
      py: 534,
      name: '边家村',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 510, // 文字位置
      textpy: 548,
      stationId: '0561'
    }, {
      px: 560,
      py: 534,
      name: '省人民医院黄雁村',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 545, // 文字位置
      textpy: 548,
      stationId: '0563'
    }, {
      px: 600,
      py: 535,
      name: '南稍门',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 575, // 文字位置
      textpy: 523,
      stationId: ['0565', '0245'],
      transfer: true
    }, {
      px: 644,
      py: 534,
      name: '文艺路',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 636, // 文字位置
      textpy: 523,
      stationId: '0567'
    }, {
      px: 688,
      py: 534,
      name: '建筑科技大学·李家村',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 548,
      stationId: ['0569', '0443'],
      transfer: true
    }, {
      px: 717,
      py: 534,
      name: '太乙路',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 700, // 文字位置
      textpy: 523,
      stationId: '0571'
    }, {
      px: 753,
      py: 534,
      name: '雁翔路北口',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 740, // 文字位置
      textpy: 523,
      stationId: '0573'
    }, {
      px: 793,
      py: 597,
      name: '青龙寺',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 803, // 文字位置
      textpy: 597,
      stationId: ['0575', '0339'],
      transfer: true
    }, {
      px: 819,
      py: 636,
      name: '理工大曲江校区',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 760, // 文字位置
      textpy: 650,
      stationId: '0577'
    }, {
      px: 855,
      py: 636,
      name: '黄渠头',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 835, // 文字位置
      textpy: 624,
      stationId: '0579'
    }, {
      px: 875,
      py: 636,
      name: '马腾空',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 880, // 文字位置
      textpy: 650,
      stationId: '0581'
    }, {
      px: 935,
      py: 636,
      name: '月登阁',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 915, // 文字位置
      textpy: 624,
      stationId: '0583',
      disabled: false
    }, {
      px: 980,
      py: 636,
      name: '雁鸣湖',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 960, // 文字位置
      textpy: 650,
      stationId: '0585',
      disabled: false
    }, {
      px: 1015,
      py: 636,
      name: '西安东站',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#C4D70D', // 线和圆的显示颜色
      textpx: 1010, // 文字位置
      textpy: 624,
      stationId: '0587',
      disabled: true
    }]
  }, // 6号线数据
  '06': {
    name: '6号线',
    px: 190,
    py: 879,
    color: '#3C4396',
    hideColor: '#d1d4f0', // 线和圆的隐藏颜色
    showColor: '#3C4396', // 线和圆的显示颜色
    ex: 0,
    ey: 405,
    data: [{
      px: 980,
      py: 447,
      name: '纺织城',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 960, // 文字位置
      textpy: 434,
      stationId: ['0921', '0683', '0157'],
      transfer: true
    }, {
      px: 980,
      py: 470,
      name: '纺二路',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 990, // 文字位置
      textpy: 475,
      stationId: '0681',
      transfer: false
    }, {
      px: 980,
      py: 495,
      name: '纺六路',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 990, // 文字位置
      textpy: 495,
      stationId: '0679',
      transfer: false,
      corner: true,
      firstStation: true
    }, {
      px: 980,
      py: 525,
      name: '',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 980, // 文字位置
      textpy: 525,
      stationId: '0677G0679'
    }, {
      px: 950,
      py: 525,
      name: '穆将王',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 950, // 文字位置
      textpy: 538,
      stationId: '0677',
      transfer: false,
      corner: true
    }, {
      px: 925,
      py: 525,
      name: '田家湾',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 910, // 文字位置
      textpy: 538,
      stationId: '0675',
      transfer: false
    }, {
      px: 900,
      py: 525,
      name: '卫星测控中心',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 880, // 文字位置
      textpy: 514,
      stationId: '0673',
      transfer: false
    }, {
      px: 875,
      py: 525,
      name: '万寿南路',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 855, // 文字位置
      textpy: 538,
      stationId: '0671',
      transfer: false
    }, {
      px: 840,
      py: 525,
      name: '公园南路',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 822, // 文字位置
      textpy: 514,
      stationId: '0669',
      transfer: false
    }, {
      px: 814,
      py: 525,
      name: '咸宁路',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 810, // 文字位置
      textpy: 538,
      stationId: ['0667', '0343'],
      transfer: true
    }, {
      px: 775,
      py: 525,
      name: '交通大学.兴庆宫',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 760, // 文字位置
      textpy: 505,
      stationId: '0665',
      transfer: false
    }, {
      px: 728,
      py: 475,
      name: '',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 725, // 文字位置
      textpy: 725,
      stationId: '0663G0665'
    }, {
      px: 719,
      py: 475,
      name: '长乐门',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 705, // 文字位置
      textpy: 465,
      stationId: '0663',
      corner: true,
      transfer: false
    }, {
      px: 689,
      py: 475,
      name: '大差市',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 649, // 文字位置
      textpy: 465,
      stationId: ['0661', '0447'],
      transfer: true
    }, {
      px: 600,
      py: 475,
      name: '钟楼',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 605, // 文字位置
      textpy: 485,
      stationId: ['0659', '0241'],
      transfer: true
    }, {
      px: 573,
      py: 475,
      name: '广济街',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 553, // 文字位置
      textpy: 488,
      stationId: '0657',
      transfer: false
    }, {
      px: 547,
      py: 475,
      name: '桥梓口',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 533, // 文字位置
      textpy: 463,
      stationId: '0655'
    }, {
      px: 522,
      py: 475,
      name: '安定门',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 508, // 文字位置
      textpy: 488,
      stationId: '0653',
      corner: true,
      firstStation: true
    }, {
      px: 502,
      py: 475,
      name: '',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 502, // 文字位置
      textpy: 475,
      stationId: '0651G0653',
      transfer: false
    }, {
      px: 502,
      py: 501,
      name: '大唐西市',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 512, // 文字位置
      textpy: 506,
      stationId: '0651',
      corner: true
    }, {
      px: 502,
      py: 534,
      name: '西北工业大学',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 475, // 文字位置
      textpy: 523,
      stationId: ['0649', '0559'],
      transfer: true
    }, {
      px: 502,
      py: 587,
      name: '科技路',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 493 + 20, // 文字位置
      textpy: 580,
      stationId: ['0647', '0327'],
      transfer: true
    }, {
      px: 502,
      py: 629,
      name: '甘家寨',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 469 + 42, // 文字位置
      textpy: 629,
      stationId: '0645'
    }, {
      px: 502,
      py: 658,
      name: '木塔寺',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 449 + 62, // 文字位置
      textpy: 658,
      stationId: '0643'
    }, {
      px: 502,
      py: 688,
      name: '省体育馆',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 502 + 10, // 文字位置
      textpy: 688 - 12,
      stationId: '0641'
    }, {
      px: 402 + 30,
      py: 732,
      name: '丈八一路',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 382 + 30, // 文字位置
      textpy: 746,
      stationId: '0639'
    }, {
      px: 372 + 15,
      py: 732,
      name: '丈八四路',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 352 + 15, // 文字位置
      textpy: 716,
      stationId: '0637'
    }, {
      px: 342 + 5,
      py: 732,
      name: '丈八六路',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 318 + 5, // 文字位置
      textpy: 746,
      stationId: '0635'
    }, {
      px: 310,
      py: 732,
      name: '造字台',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 260, // 文字位置
      textpy: 732,
      stationId: '0633'
    }, {
      px: 310,
      py: 766,
      name: '西部大道(西太路口)',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 320, // 文字位置
      textpy: 766,
      stationId: '0631'
    }, {
      px: 310,
      py: 794,
      name: '郭杜西',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 320, // 文字位置
      textpy: 794,
      stationId: '0629'
    }, {
      px: 294,
      py: 808,
      name: '仁村',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 304, // 文字位置
      textpy: 818,
      stationId: '0627'
    }, {
      px: 276,
      py: 826,
      name: '西安国际医学中心',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 286, // 文字位置
      textpy: 838,
      stationId: '0625'
    }, {
      px: 254,
      py: 848,
      name: '西电科大南校区·未来之瞳',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 264, // 文字位置
      textpy: 858,
      stationId: '0623',
      disabled: false
    }, {
      px: 232,
      py: 870,
      name: '西安南站',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#3C4396', // 线和圆的显示颜色
      textpx: 242, // 文字位置
      textpy: 880,
      stationId: '0621',
      disabled: false
    }]
  }, // 9号线数据
  '08': {
    name: '8号线',
    px: 0,
    py: 0,
    color: '#dfab1a',
    hideColor: '#CEE4EA', // 线和圆的隐藏颜色
    showColor: '#dfab1a', // 线和圆的显示颜色
    ex: 0,
    ey: 0,
    data: [{
      px: 720,
      py: 302,
      name: '井上村',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 705, // 文字位置
      textpy: 302 - 10,
      stationId: ['1053', '0801'],
      transfer: true,
      corner: false,
      firstStation: true
    },
    {
      px: 689,
      py: 302,
      name: '余家寨',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 650, // 文字位置
      textpy: 302 - 10,
      stationId: ['0459', '0802'],
      transfer: true,
      corner: false,
      firstStation: false
    },
    {
      px: 689 - 35,
      py: 302,
      name: '市第三医院',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 689 - 70, // 文字位置
      textpy: 302 + 17,
      stationId: '0803',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 600,
      py: 302,
      name: '市图书馆',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 544 + 50, // 文字位置
      textpy: 307 - 15,
      stationId: ['0804', '0231'],
      transfer: true,
      corner: false,
      firstStation: false
    },
    {
      px: 600 - 35,
      py: 302,
      name: '霸城门',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 544 - 7, // 文字位置
      textpy: 307 + 12,
      stationId: '0805',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 600 - 70,
      py: 302,
      name: '',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 544 - 7, // 文字位置
      textpy: 307 + 12,
      stationId: ''
      // transfer: false,
      // corner: false,
      // firstStation: false
    },
    {
      px: 600 - 70,
      py: 302 + 35,
      name: '大风阁',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 600 - 70 - 50, // 文字位置
      textpy: 302 + 35,
      stationId: '0806',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 600 - 70,
      py: 302 + 70,
      name: '红庙坡',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 600 - 70 - 50, // 文字位置
      textpy: 302 + 70,
      stationId: '0807',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 600 - 70,
      py: 302 + 105,
      name: '景曜门',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 600 - 70 - 44, // 文字位置
      textpy: 302 + 92,
      stationId: '0808',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 600 - 70 - 35,
      py: 302 + 105,
      name: '光化门',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 600 - 105 - 50, // 文字位置
      textpy: 302 + 95,
      stationId: '0809',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 440,
      py: 302 + 105,
      name: '',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 600 - 105 - 50, // 文字位置
      textpy: 302 + 95,
      stationId: '',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 440,
      py: 302 + 120,
      name: '白家口',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 440 + 8, // 文字位置
      textpy: 302 + 120 + 5,
      stationId: '0810',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 440,
      py: 447,
      name: '开远门',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 455 - 28 - 30, // 文字位置
      textpy: 435 - 5,
      stationId: ['0811', '0131'],
      transfer: true,
      corner: false,
      firstStation: false
    },
    {
      px: 440,
      py: 447 + 40,
      name: '土门',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 440 - 35, // 文字位置
      textpy: 447 + 40,
      stationId: '0812',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 440,
      py: 534,
      name: '金光门',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 425 - 25, // 文字位置
      textpy: 523 - 7,
      stationId: ['0813', '0555'],
      transfer: true,
      corner: false,
      firstStation: false
    },
    {
      px: 440,
      py: 576,
      name: '延平门',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 425 - 30, // 文字位置
      textpy: 590 - 25,
      stationId: ['0814', '0325'],
      transfer: true,
      corner: false,
      firstStation: false
    },
    {
      px: 440,
      py: 634,
      name: '科技二路',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 375, // 文字位置
      textpy: 639,
      stationId: '0815',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 440,
      py: 688,
      name: '木塔寺西',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 380, // 文字位置
      textpy: 688,
      stationId: '0816',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 502,
      py: 688,
      name: '省体育馆',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 502 + 10, // 文字位置
      textpy: 688 - 12,
      stationId: ['0641', '0817'],
      transfer: true,
      corner: false,
      firstStation: false
    },
    {
      px: 502 + 30,
      py: 688,
      name: '山门口',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 502, // 文字位置
      textpy: 688 + 20,
      stationId: '0818',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 502 + 55,
      py: 688,
      name: '安化门',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 502 + 40, // 文字位置
      textpy: 688 + 20,
      stationId: '0819',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 502 + 75,
      py: 688,
      name: '东仪路',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 502 + 65 - 5, // 文字位置
      textpy: 688 - 15,
      stationId: '0820',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 600,
      py: 688,
      name: '电视塔',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 610, // 文字位置
      textpy: 688 - 15,
      stationId: ['0253', '0821'],
      transfer: true,
      corner: false,
      firstStation: false
    },
    {
      px: 600 + 40,
      py: 688,
      name: '大唐不夜城',
      hideColor: '#E3CACA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 600 + 10, // 文字位置
      textpy: 688 + 15,
      stationId: '0822',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 689,
      py: 688,
      name: '曲江池西',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 695, // 文字位置
      textpy: 688 - 15,
      stationId: ['0435', '0823'],
      transfer: true,
      corner: false,
      firstStation: false
    },
    {
      px: 689 + 40,
      py: 688,
      name: '寒窑',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 689 + 40, // 文字位置
      textpy: 688 + 15,
      stationId: '0824',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 689 + 80,
      py: 688,
      name: '新开门',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 689 + 80, // 文字位置
      textpy: 688 + 15,
      stationId: '0825',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 689 + 120,
      py: 688,
      name: '缪家寨',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 689 + 120, // 文字位置
      textpy: 688 + 15,
      stationId: '0826',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 875,
      py: 688,
      name: '植物园',
      hideColor: '#CEE4EA', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 875 + 10, // 文字位置
      textpy: 688,
      stationId: '0827',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 875,
      py: 636,
      name: '马腾空',
      hideColor: '#ddded0', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 880, // 文字位置
      textpy: 650,
      stationId: ['0581', '0828'],
      transfer: true,
      corner: false,
      firstStation: false
    },
    {
      px: 875,
      py: 525 + 80,
      name: '东等驾坡',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 875 + 10, // 文字位置
      textpy: 525 + 80,
      stationId: '0829',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 875,
      py: 525 + 40,
      name: '西等驾坡',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 875 + 10, // 文字位置
      textpy: 525 + 40,
      stationId: '0830',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 875,
      py: 525,
      name: '万寿南路',
      hideColor: '#d1d4f0', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 855, // 文字位置
      textpy: 538,
      stationId: ['0671', '0831'],
      transfer: true,
      corner: false,
      firstStation: false
    },
    {
      px: 860 + 15,
      py: 447 + 30,
      name: '韩森寨',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 860 + 25, // 文字位置
      textpy: 447 + 33,
      stationId: '0832',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 860 + 15,
      py: 447,
      name: '万寿路',
      hideColor: '#CBE1F2', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 845 - 10, // 文字位置
      textpy: 460,
      stationId: ['0149', '0833'],
      transfer: true,
      corner: false,
      firstStation: false
    },
    {
      px: 863 + 12,
      py: 329 + 60,
      name: '幸福林带北',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 863 + 20, // 文字位置
      textpy: 329 + 60,
      stationId: '0834',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 863 + 12,
      py: 329 + 30,
      name: '米家崖',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 863 + 20, // 文字位置
      textpy: 329 + 30,
      stationId: '0835',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 863 + 12,
      py: 329,
      name: '广泰门',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 848 - 12, // 文字位置
      textpy: 344,
      stationId: ['0355', '0836'],
      transfer: true,
      corner: false,
      firstStation: false
    },
    {
      px: 863 + 12,
      py: 302,
      name: '',
      hideColor: '#DFD9E9', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 848 - 22, // 文字位置
      textpy: 344
    },
    {
      px: 720 + 40,
      py: 302,
      name: '北辰东路',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 720 + 50, // 文字位置
      textpy: 302 -10 ,
      stationId: '0837',
      transfer: false,
      corner: false,
      firstStation: false
    },
    {
      px: 720,
      py: 302,
      name: '井上村',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#dfab1a', // 线和圆的显示颜色
      textpx: 705, // 文字位置
      textpy: 302 - 10,
      stationId: ['0355', '0836', '0838'],
      transfer: true,
      corner: false,
      firstStation: false
    }
    ]
  },
  '09': {
    name: '9号线',
    px: 0,
    py: 452,
    color: '#FF9D10',
    hideColor: '#f6e3c8', // 线和圆的隐藏颜色
    showColor: '#FF9D10', // 线和圆的显示颜色
    ex: 1160,
    ey: 20,
    data: [{
      px: 980,
      py: 447,
      name: '纺织城',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 960, // 文字位置
      textpy: 434,
      stationId: ['0921', '0683', '0157'],
      transfer: true
    }, {
      px: 1012,
      py: 446,
      name: '香王',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1000, // 文字位置
      textpy: 434,
      stationId: '0923'
    }, {
      px: 1039,
      py: 446,
      name: '灞柳二路',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1050, // 文字位置
      textpy: 446,
      stationId: '0925'
    }, {
      px: 1039,
      py: 416,
      name: '田王',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1050, // 文字位置
      textpy: 416,
      stationId: '0927'
    }, {
      px: 1039,
      py: 386,
      name: '洪庆',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1050, // 文字位置
      textpy: 386,
      stationId: '0929'
    }, {
      px: 1059,
      py: 356,
      name: '紫霞三路',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1070, // 文字位置
      textpy: 356,
      stationId: '0931'
    }, {
      px: 1079,
      py: 326,
      name: '凤凰池',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1090, // 文字位置
      textpy: 326,
      stationId: '0933'
    }, {
      px: 1099,
      py: 296,
      name: '鹦鹉寺公园',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1110, // 文字位置
      textpy: 296,
      stationId: '0935'
    }, {
      px: 1119,
      py: 266,
      name: '芷阳广场',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1130, // 文字位置
      textpy: 266,
      stationId: '0937'
    }, {
      px: 1141,
      py: 230,
      name: '西工程大·西科大(临潼校区)',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 990, // 文字位置
      textpy: 230,
      stationId: '0939'
    }, {
      px: 1161,
      py: 230,
      name: '西花园',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1141, // 文字位置
      textpy: 217,
      stationId: '0941'
    }, {
      px: 1182,
      py: 230,
      name: '华清池',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1165, // 文字位置
      textpy: 246,
      stationId: '0943'
    }, {
      px: 1182,
      py: 180,
      name: '东三岔',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1136, // 文字位置
      textpy: 180,
      stationId: '0945'
    }, {
      px: 1182,
      py: 126,
      name: '银桥大道',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1124, // 文字位置
      textpy: 126,
      stationId: '0947'
    }, {
      px: 1182,
      py: 70,
      name: '秦陵西',
      hideColor: '#f6e3c8', // 线和圆的隐藏颜色
      showColor: '#FF9D10', // 线和圆的显示颜色
      textpx: 1136, // 文字位置
      textpy: 70,
      stationId: '0949'
    }]
  }, // 10号线数据
  '10': {
    name: '10号线',
    px: 1150,
    py: -100,
    color: '#00954d',
    hideColor: '#79c6a1', // 线和圆的隐藏颜色
    showColor: '#00954d', // 线和圆的显示颜色
    data: [{
      px: 720,
      py: 302,
      name: '井上村',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 705, // 文字位置
      textpy: 302 - 10,
      stationId: '1053'
    },
    {
      px: 720,
      py: 265 - 20,
      name: '帽珥冢',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 730, // 文字位置
      textpy: 265 - 20,
      stationId: '1051'
    },
    {
      px: 720,
      py: 210 - 20,
      name: '团结村',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 730, // 文字位置
      textpy: 210 - 20,
      stationId: '1049'
    },
    {
      px: 720,
      py: 150 - 20,
      name: '红旗厂',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#02AF8E', // 线和圆的显示颜色
      textpx: 730, // 文字位置
      textpy: 150 - 20,
      stationId: '1047'
    },
    {
      px: 720,
      py: 90,
      name: '西安工大·武德路',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 630 + 90, // 文字位置
      textpy: 75,
      stationId: ['1045', '1443'],
      transfer: true,
      showNameFlag: true
    },
    {
      px: 720,
      py: 70 - 70,
      name: '景家堡',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 697, // 文字位置
      textpy: 55 - 70,
      stationId: '1043'
    },
    {
      px: 760 + 32,
      py: 70 - 70,
      name: '未央湖',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 746 + 32, // 文字位置
      textpy: 55 - 70,
      stationId: '1041'
    },
    {
      px: 800 + 52,
      py: 70 - 70,
      name: '西堡',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 795 + 52, // 文字位置
      textpy: 55 - 70,
      stationId: '1039'
    },
    {
      px: 930,
      py: 70 - 70,
      name: '水流',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 930 + 10, // 文字位置
      textpy: 70 - 70,
      stationId: '1037'
    },
    {
      px: 930,
      py: 50 - 80,
      name: '泾渭半岛',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 930 + 10, // 文字位置
      textpy: 50 - 80,
      stationId: '1035'
    },
    {
      px: 930,
      py: 30 - 90,
      name: '杨官寨',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 930 - 50, // 文字位置
      textpy: 30 - 90,
      stationId: '1033'
    },
    {
      px: 930,
      py: 8 - 100,
      name: '军庄',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 930 - 40, // 文字位置
      textpy: 10 - 100,
      stationId: '1031'
    },
    {
      px: 930 + 31,
      py: 8 - 100,
      name: '崇皇',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 930 + 18, // 文字位置
      textpy: 23 - 100 - 30,
      stationId: '1029'
    },
    {
      px: 930 + 62 + 20,
      py: 8 - 100,
      name: '榆楚',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 930 + 62 + 10, // 文字位置
      textpy: 23 - 100,
      stationId: '1027'
    },
    {
      px: 930 + 91 + 30,
      py: 8 - 100,
      name: '鹿苑大道',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 930 + 91 + 10, // 文字位置
      textpy: 23 - 100 - 30,
      stationId: '1025'
    },
    {
      px: 930 + 122 + 40,
      py: 8 - 100,
      name: '杏王',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 930 + 122 + 30, // 文字位置
      textpy: 23 - 100,
      stationId: '1023'
    },
    {
      px: 930 + 150 + 50,
      py: 8 - 100,
      name: '昭慧广场',
      hideColor: '#79c6a1', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 930 + 150 + 30, // 文字位置
      textpy: 23 - 100 - 30,
      stationId: '1021'
    }
    ]
  }, // 14号线数据
  '14': {
    name: '14号线',
    px: 30,
    py: 90,
    color: '#6BC4D6',
    hideColor: '#d0e9ec', // 线和圆的隐藏颜色
    showColor: '#6BC4D6', // 线和圆的显示颜色
    ex: 0,
    ey: 50,
    data: [{
      px: 1060,
      py: 90,
      name: '贺韶',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 1055, // 文字位置
      textpy: 78,
      stationId: '1455'
    }, {
      px: 1020,
      py: 90,
      name: '港务大道',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 997, // 文字位置
      textpy: 78,
      stationId: '1453'
    }, {
      px: 970,
      py: 90,
      name: '新寺',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 960, // 文字位置
      textpy: 78,
      stationId: '1451'
    }, {
      px: 930,
      py: 90,
      name: '双寨',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 900, // 文字位置
      textpy: 80,
      stationId: ['1449', '0367'],
      transfer: true
    }, {
      px: 840,
      py: 90,
      name: '奥体中心',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 820, // 文字位置
      textpy: 105,
      stationId: '1447'
    }, {
      px: 770,
      py: 90,
      name: '北辰',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 760, // 文字位置
      textpy: 105,
      stationId: '1445'
    }, {
      px: 720,
      py: 90,
      name: '西安工大·武德路',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 630 + 90, // 文字位置
      textpy: 75,
      stationId: ['1045', '1443'],
      transfer: true,
      showNameFlag: true
    }, {
      px: 670,
      py: 90,
      name: '文景山公园',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 640, // 文字位置
      textpy: 80,
      stationId: '1441'
    }, {
      px: 599,
      py: 90,
      name: '西安北站',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 539, // 文字位置
      textpy: 107,
      stationId: ['1439', '0477', '0221'],
      transfer: true
    }, {
      px: 550,
      py: 69,
      name: '渭河南',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 554, // 文字位置
      textpy: 62,
      stationId: '1437'
    }, {
      px: 502,
      py: 52,
      name: '秦宫',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 490, // 文字位置
      textpy: 38,
      stationId: '1435'
    }, {
      px: 450,
      py: 68,
      name: '秦汉新城',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 430, // 文字位置
      textpy: 84,
      stationId: '1433'
    }, {
      px: 400,
      py: 83,
      name: '长陵',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 400, // 文字位置
      textpy: 100,
      stationId: '1431'
    }, {
      px: 353,
      py: 96,
      name: '摆旗寨',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 335, // 文字位置
      textpy: 113,
      stationId: '1429'
    }, {
      px: 300,
      py: 84,
      name: '艺术中心',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 303, // 文字位置
      textpy: 73,
      stationId: '1427'
    }, {
      px: 250,
      py: 72,
      name: '空港新城',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 253, // 文字位置
      textpy: 63,
      stationId: '1425'
    }, {
      px: 200,
      py: 60,
      name: '机场（T5）',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 177, // 文字位置
      textpy: 47,
      stationId: '1423',
    //   disabled: true
    }, {
      px: 100,
      py: 95,
      name: '机场西（T1、T2、T3）',
      hideColor: '#d0e9ec', // 线和圆的隐藏颜色
      showColor: '#6BC4D6', // 线和圆的显示颜色
      textpx: 80, // 文字位置
      textpy: 110,
      stationId: '1421'
    }]
  }, // 16号线数据
  '16': {
    name: '16号线',
    px: 0,
    py: 580,
    color: '#ef8d6a',
    hideColor: '#f1ded7', // 线和圆的隐藏颜色
    showColor: '#ef8d6a', // 线和圆的显示颜色
    ex: 230,
    ey: 240,
    data: [{
      px: 250,
      py: 280,
      name: '秦创原中心',
      hideColor: '#f1ded7', // 线和圆的隐藏颜色
      showColor: '#ef8d6a', // 线和圆的显示颜色
      textpx: 260, // 文字位置
      textpy: 280,
      stationId: '1637'
    }, {
      px: 250,
      py: 330,
      name: '西咸大厦',
      hideColor: '#f1ded7', // 线和圆的隐藏颜色
      showColor: '#ef8d6a', // 线和圆的显示颜色
      textpx: 260, // 文字位置
      textpy: 330,
      stationId: '1635'
    }, {
      px: 250,
      py: 370,
      name: '上林路',
      hideColor: '#f1ded7', // 线和圆的隐藏颜色
      showColor: '#ef8d6a', // 线和圆的显示颜色
      textpx: 255, // 文字位置
      textpy: 390,
      stationId: ['1633', '0117'],
      transfer: true
    }, {
      px: 250,
      py: 405,
      name: '复兴大道北',
      hideColor: '#f1ded7', // 线和圆的隐藏颜色
      showColor: '#ef8d6a', // 线和圆的显示颜色
      textpx: 255, // 文字位置
      textpy: 415,
      stationId: '1631'
    }, {
      px: 250,
      py: 440,
      name: '西安国际足球中心',
      hideColor: '#f1ded7', // 线和圆的隐藏颜色
      showColor: '#ef8d6a', // 线和圆的显示颜色
      textpx: 150, // 文字位置
      textpy: 440,
      stationId: '1629'
    }, {
      px: 250,
      py: 475,
      name: '细柳营',
      hideColor: '#f1ded7', // 线和圆的隐藏颜色
      showColor: '#ef8d6a', // 线和圆的显示颜色
      textpx: 205, // 文字位置
      textpy: 475,
      stationId: '1627'
    }, {
      px: 250,
      py: 510,
      name: '沣东城市广场',
      hideColor: '#f1ded7', // 线和圆的隐藏颜色
      showColor: '#ef8d6a', // 线和圆的显示颜色
      textpx: 260, // 文字位置
      textpy: 500,
      stationId: '1625',
      corner: true,
      firstStation: true
    }, {
      px: 190,
      py: 510,
      name: '',
      hideColor: '#f1ded7', // 线和圆的隐藏颜色
      showColor: '#ef8d6a', // 线和圆的显示颜色
      textpx: 930, // 文字位置
      textpy: 310,
      stationId: '1623G1625'
    }, {
      px: 190,
      py: 534,
      name: '欢乐谷',
      hideColor: '#f1ded7', // 线和圆的隐藏颜色
      showColor: '#ef8d6a', // 线和圆的显示颜色
      textpx: 152, // 文字位置
      textpy: 549,
      stationId: ['1623', '0537'],
      transfer: true,
      corner: true
    }, {
      px: 190,
      py: 600,
      name: '诗经里',
      hideColor: '#f1ded7', // 线和圆的隐藏颜色
      showColor: '#ef8d6a', // 线和圆的显示颜色
      textpx: 200, // 文字位置
      textpy: 600,
      stationId: '1621'
    }]
  }, // 西户线数据
  'xh': {
    name: '西户线',
    px: 72,
    py: 870,
    ex: 0,
    ey: 0,
    color: '#8f4b81',
    hideColor: '#d0a1c7', // 线和圆的隐藏颜色
    showColor: '#8f4b81', // 线和圆的显示颜色
    data: [{
      px: 330,
      py: 534,
      name: '阿房宫南',
      hideColor: '#d0a1c7', // 线和圆的隐藏颜色
      showColor: '#8f4b81', // 线和圆的显示颜色
      textpx: 310, // 文字位置
      textpy: 523,
      transfer: true,
      stationId: ['0547', 'xh01']
    }, {
      px: 130,
      py: 880,
      name: '户县',
      hideColor: '#d0a1c7', // 线和圆的隐藏颜色
      showColor: '#8f4b81', // 线和圆的显示颜色
      textpx: 139, // 文字位置
      textpy: 890,
      stationId: 'xh02'
    }]
  }
}

// 换乘点数据
const transitionStation = [{
  name: '行政中心', id: ['0467', '0227'], px: 600, py: 221
}, {
  name: '北大街', id: ['0139', '0239'], px: 600, py: 446.5
}, {
  name: '西安工大·武德路', id: ['1443', '1045'], px: 720, py: 90
}, {
  name: '阿房宫南', id: ['0547', 'xh01'], px: 330, py: 534
}, {
  name: '小寨', px: 600, py: 600
}, {
  name: '五路口', px: 689, py: 447.5
}, {
  name: '大雁塔', px: 688, py: 600
}, {
  name: '通化门', px: 813, py: 446
}, {
  name: '西北工业大学', px: 502, py: 534
}, {
  name: '南稍门', px: 600, py: 535
}, {
  name: '建筑科技大学·李家村', px: 688, py: 534
}, {
  name: '青龙寺', px: 793, py: 597
}, {
  name: '科技路', px: 483, py: 583
}, {
  name: '纺织城', px: 980, py: 447
}, {
  name: '西安北站', px: 600, py: 90
}, {
  name: '西安北站', px: 600, py: 107
}, {
  name: '双寨', px: 930, py: 90
}, {
  name: '钟楼', px: 600, py: 475
}, {
  name: '大差市', px: 689, py: 475
}, {
  name: '咸宁路', px: 814, py: 525
}, {
  name: '市图书馆', px: 600, py: 302
}, {
  name: '开远门', px: 440, py: 447
}, {
  name: '金光门', px: 440, py: 534
}, {
  name: '延平门', px: 440, py: 576
}, {
  name: '上林路', px: 250, py: 370
}, {
  name: '省体育馆', px: 502, py: 688
}, {
  name: '电视塔', px: 600, py: 688
},{
  name: '曲江池西', px: 689, py: 688
},{
  name: '马腾空', px: 875, py: 636
},{
  name: '万寿南路', px: 875, py: 525
},{
  name: '万寿路', px: 860 + 15, py: 447
},{
  name: '广泰门', px: 863 + 12, py: 329
},{
  name: '井上村', px: 720, py: 302
},{
  name: '余家寨', px: 689, py: 302
},{
  name: '欢乐谷', px: 190, py: 534
}]

// U型数据
const Udata = {
  path1: `
          M590 298
          L590 310
          M590 310
          Q 599 325 608 310
          M608 310
          L608 298`,
  path2: `
        M608 298
        L608 288
        M608 288
        Q599 274 590 288
        M 590 288
        L590 298
  `
}
