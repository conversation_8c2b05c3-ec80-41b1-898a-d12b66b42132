{"name": "metro_ai", "version": "1.0.0", "description": "西安地铁线路图 - 现代化的交互式地铁线路图应用程序", "main": "src/app.js", "type": "module", "scripts": {"start": "python -m http.server 8000", "dev": "python -m http.server 8000", "serve": "npx http-server -p 8000", "validate": "node -e \"import('./src/utils/validation.js').then(m => console.log('Validation module loaded successfully'))\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["metro", "subway", "map", "xian", "interactive", "svg", "javascript", "es6"], "author": "Metro AI Team", "license": "MIT", "private": true, "engines": {"node": ">=14.0.0"}, "browserslist": ["Chrome >= 60", "Firefox >= 55", "Safari >= 12", "Edge >= 79"]}